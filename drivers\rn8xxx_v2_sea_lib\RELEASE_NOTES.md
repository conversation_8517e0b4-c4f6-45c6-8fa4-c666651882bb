# Change Log

## v1.1.1 (2025-1-8)

### 改动点

- 新增单相SOC V3版本TRNG支持
- 修复TRNG使用结束后未关闭的bug

## v1.1.0 (2024-11-19)

### 改动点

- 新增RSA库函数接口

## v1.0.7 (2024-08-13)

### 改动点

- 支持三相SOC V2版SEA全部接口(需要正确定义芯片类型宏定义，具体请参考ll库说明)
- 支持单相SOC V3版AES和hash(SHA1,SHA224,SHA256)

## v1.0.6 (2024-07-31)

### 改动点

- 增加版本号获取接口（仅适用于二进制库文件），接口为`LL_AES_version`

## v1.0.5 (2023-09-07)

### 改动点

###AES改动

- 增加AES 128bit有限域乘法驱动函数aes_drv_gf128_mult

###HASH

- 修改bug：hash计算过程中进行现场保存和恢复，当中间数据长度为非64字节/128字节对齐的时候，后续如果接着开启一次新的hash计算，则会计算错误。

###ECC
- 增加指定私钥，生成公钥函数ecc_public_keygen
- 增加指定私钥，生成签名函数ecc_specify_pkey_sign_gen

## v1.0.4 (2023-06-07)

### 改动点
####总体优化
- 修改接口返回ErrStatus,将SUCCESS = 1修改为SUCCESS = 0;
- 根据MISRA C 2004/2012标准修改库函数，消除错误提示。
  MISRA 未通过：
    MISRA 2004: 11.1/16.9
    MISRA 2012: 10.3

###AES改动
  1. 增加AES 明文、密文、IV数据、ADD数据地址对齐方式 U8/U32配置项。默认U8。
  2. 将AES key,tag地址对齐方式修改为U8。
  3. 函数初始化完毕后，iv,add, key地址指针对应的数据内容可以销毁，不再影响后续加解密。
  4. 增加在AES加解密过程保存和恢复函数。
  5. 增加ADD数据单纯注册和认证函数GMAC。
###HASH
- 增加HASH消息传入和结果输出地址对齐方式 U8/U32配置项。默认U8。
- 增加在HASH计算过程保存和恢复函数。

## v1.0.3 (2023-04-19)

### 改动点
####应用优化
- 修改AES GCM模式。当TAG错误的时候，仍然进行GCM解密。

## v1.0.2 (2023-04-11)

### 改动点
####修改bug
- 修改AES GCM模式。GHASH计算buffer空间没有清零，历史数据残留导致GHASH计算错误bug。


## v1.0.1 (2023-03-28)

### 改动点
####增加功能

- 增加ECDH256、ECDH384
####修改bug
- 修改AES GCM模式，当明文长度非word对齐的时候，大端数据尾端填充位置错误的bug。
####文档优化
- 消除pdf转化时候，因为文本过长，导致字体被截断的现象。包含:AES库函数使用说明.pdf,HASH库函数使用说明.pdf


## v1.0.0 (2023-2-117)

### 新建
- support SHA1/SHA224/SHA256/SHA384/SHA512
- support AES-GCM AES-ECB  AES-CBC AES-CTR AES-CFB128 ASE-OFB
- support ECDSA p256、p384
- support TRNG
