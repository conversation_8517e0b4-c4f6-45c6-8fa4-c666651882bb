#include "main.h"
#ifdef LL_INTC_DEMO
#include "rn8xxx_ll_intc_demo.h"

#define BSP_INTC_MODE INTC_INDEPEND_EXTX
// #define BSP_INTC_MODE INTC_SHARE_EXT0

#if defined(RN821x_RN721x_SOC_V2)
#define TEST_INTC_ID140
#elif defined(RN821x_RN721x_SOC_V3)
#define TEST_INTC_ID581
// #define TEST_INTC_ID140
#elif defined(RN831x_RN861x_MCU_V2)
#define TEST_INTC_ID131
#elif defined(RN831x_RN861x_MCU_V3)
// #define TEST_INTC_ID010
// #define TEST_INTC_ID074
// #define TEST_INTC_ID111
// #define TEST_INTC_ID140
// #define TEST_INTC_ID175
// #define TEST_INTC_ID232
// #define TEST_INTC_ID276
// #define TEST_INTC_ID341
// #define TEST_INTC_ID377
// #define TEST_INTC_ID442
// #define TEST_INTC_ID480
// #define TEST_INTC_ID543
// #define TEST_INTC_ID581

// #define TEST_INTC_ID621
// #define TEST_INTC_ID682
// #define TEST_INTC_ID636
// #define TEST_INTC_ID723
// #define TEST_INTC_ID783
#define TEST_INTC_ID737
#elif defined(RN202x_RN7326_SOC_V2)
// #define TEST_INTC_ID070
// #define TEST_INTC_ID084
// #define TEST_INTC_ID106
// #define TEST_INTC_ID171
// #define TEST_INTC_ID185
// #define TEST_INTC_ID210
// #define TEST_INTC_ID272
// #define TEST_INTC_ID286
// #define TEST_INTC_ID316
// #define TEST_INTC_ID373
// #define TEST_INTC_ID387
// #define TEST_INTC_ID417
// #define TEST_INTC_ID474
// #define TEST_INTC_ID490
// #define TEST_INTC_ID575
// #define TEST_INTC_ID591
// #define TEST_INTC_ID644
// #define TEST_INTC_ID676
// #define TEST_INTC_ID745
#define TEST_INTC_ID777
#else
#define TEST_INTC_ID131
#endif

#if defined(TEST_INTC_ID010)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC0_ID
#define BSP_INTC_ID INTC_ID0
#define BSP_INTC_IO PIN_1_0
#elif defined(TEST_INTC_ID070)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC0_ID
#define BSP_INTC_ID INTC_ID0
#define BSP_INTC_IO PIN_7_0
#elif defined(TEST_INTC_ID074)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC0_ID
#define BSP_INTC_ID INTC_ID0
#define BSP_INTC_IO PIN_7_4
#elif defined(TEST_INTC_ID084)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC0_ID
#define BSP_INTC_ID INTC_ID0
#define BSP_INTC_IO PIN_8_4
#elif defined(TEST_INTC_ID111)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC1_ID
#define BSP_INTC_ID INTC_ID1
#define BSP_INTC_IO PIN_1_1

#elif defined(TEST_INTC_ID106)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC1_ID
#define BSP_INTC_ID INTC_ID1
#define BSP_INTC_IO PIN_0_6
#elif defined(TEST_INTC_ID171)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC1_ID
#define BSP_INTC_ID INTC_ID1
#define BSP_INTC_IO PIN_7_1
#elif defined(TEST_INTC_ID185)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC1_ID
#define BSP_INTC_ID INTC_ID1
#define BSP_INTC_IO PIN_8_5
#elif defined(TEST_INTC_ID131)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC1_ID
#define BSP_INTC_ID INTC_ID1
#define BSP_INTC_IO PIN_3_1
#elif defined(TEST_INTC_ID140)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC1_ID
#define BSP_INTC_ID INTC_ID1
#define BSP_INTC_IO PIN_4_0
#elif defined(TEST_INTC_ID175)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC1_ID
#define BSP_INTC_ID INTC_ID1
#define BSP_INTC_IO PIN_7_5

#elif defined(TEST_INTC_ID210)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC2_ID
#define BSP_INTC_ID INTC_ID2
#define BSP_INTC_IO PIN_1_0
#elif defined(TEST_INTC_ID272)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC2_ID
#define BSP_INTC_ID INTC_ID2
#define BSP_INTC_IO PIN_7_2
#elif defined(TEST_INTC_ID286)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC2_ID
#define BSP_INTC_ID INTC_ID2
#define BSP_INTC_IO PIN_8_6
#elif defined(TEST_INTC_ID227)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC2_ID
#define BSP_INTC_ID INTC_ID2
#define BSP_INTC_IO PIN_2_7
#elif defined(TEST_INTC_ID232)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC2_ID
#define BSP_INTC_ID INTC_ID2
#define BSP_INTC_IO PIN_3_2
#elif defined(TEST_INTC_ID276)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC2_ID
#define BSP_INTC_ID INTC_ID2
#define BSP_INTC_IO PIN_7_6

#elif defined(TEST_INTC_ID316)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC3_ID
#define BSP_INTC_ID INTC_ID3
#define BSP_INTC_IO PIN_1_6
#elif defined(TEST_INTC_ID373)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC3_ID
#define BSP_INTC_ID INTC_ID3
#define BSP_INTC_IO PIN_7_3
#elif defined(TEST_INTC_ID387)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC3_ID
#define BSP_INTC_ID INTC_ID3
#define BSP_INTC_IO PIN_8_7
#elif defined(TEST_INTC_ID341)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC3_ID
#define BSP_INTC_ID INTC_ID3
#define BSP_INTC_IO PIN_4_1
#elif defined(TEST_INTC_ID377)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC3_ID
#define BSP_INTC_ID INTC_ID3
#define BSP_INTC_IO PIN_7_7

#elif defined(TEST_INTC_ID417)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC4_ID
#define BSP_INTC_ID INTC_ID4
#define BSP_INTC_IO PIN_1_7
#elif defined(TEST_INTC_ID474)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC4_ID
#define BSP_INTC_ID INTC_ID4
#define BSP_INTC_IO PIN_7_4
#elif defined(TEST_INTC_ID490)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC4_ID
#define BSP_INTC_ID INTC_ID4
#define BSP_INTC_IO PIN_9_0
#elif defined(TEST_INTC_ID442)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC4_ID
#define BSP_INTC_ID INTC_ID4
#define BSP_INTC_IO PIN_4_2
#elif defined(TEST_INTC_ID480)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC4_ID
#define BSP_INTC_ID INTC_ID4
#define BSP_INTC_IO PIN_8_0

#elif defined(TEST_INTC_ID575)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC5_ID
#define BSP_INTC_ID INTC_ID5
#define BSP_INTC_IO PIN_7_5
#elif defined(TEST_INTC_ID591)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC5_ID
#define BSP_INTC_ID INTC_ID5
#define BSP_INTC_IO PIN_9_1
#elif defined(TEST_INTC_ID543)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC5_ID
#define BSP_INTC_ID INTC_ID5
#define BSP_INTC_IO PIN_4_3
#elif defined(TEST_INTC_ID581)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC5_ID
#define BSP_INTC_ID INTC_ID5
#define BSP_INTC_IO PIN_8_1
#elif defined(TEST_INTC_ID5146)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC5_ID
#define BSP_INTC_ID INTC_ID5
#define BSP_INTC_IO PIN_14_6

#elif defined(TEST_INTC_ID644)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC6_ID
#define BSP_INTC_ID INTC_ID6
#define BSP_INTC_IO PIN_4_4
#elif defined(TEST_INTC_ID676)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC6_ID
#define BSP_INTC_ID INTC_ID6
#define BSP_INTC_IO PIN_7_6
#elif defined(TEST_INTC_ID621)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC6_ID
#define BSP_INTC_ID INTC_ID6
#define BSP_INTC_IO PIN_2_1
#elif defined(TEST_INTC_ID682)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC6_ID
#define BSP_INTC_ID INTC_ID6
#define BSP_INTC_IO PIN_8_2
#elif defined(TEST_INTC_ID636)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC6_ID
#define BSP_INTC_ID INTC_ID6
#define BSP_INTC_IO PIN_3_6

#elif defined(TEST_INTC_ID745)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC7_ID
#define BSP_INTC_ID INTC_ID7
#define BSP_INTC_IO PIN_4_5
#elif defined(TEST_INTC_ID777)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC7_ID
#define BSP_INTC_ID INTC_ID7
#define BSP_INTC_IO PIN_7_7
#elif defined(TEST_INTC_ID723)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC7_ID
#define BSP_INTC_ID INTC_ID7
#define BSP_INTC_IO PIN_2_3
#elif defined(TEST_INTC_ID737)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC7_ID
#define BSP_INTC_ID INTC_ID7
#define BSP_INTC_IO PIN_3_7
#elif defined(TEST_INTC_ID783)
#define BSP_SYSC_INTC_ID LL_SYSC_INTC7_ID
#define BSP_INTC_ID INTC_ID7
#define BSP_INTC_IO PIN_8_3

#endif
void BSP_INTC_Interrupt(eINTC_ID_TypeDef INTC_Id)
{
  uint32_t status;

  status = GET_REG_INTC_STA(INTC, INTC_Id);

  LL_GPIO_OverturnPin(PIN_0_0);

  SET_REG_INTC_STA(INTC, status);
}

#ifdef RN202x_RN7326_SOC_V2
void EXT_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
#else
void EXT0_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
void EXT1_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
void EXT2_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
void EXT3_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
void EXT4_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}

void EXT5_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
void EXT6_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
void EXT7_HANDLER(void)
{
  BSP_INTC_Interrupt(BSP_INTC_ID);
}
#endif
void LL_INTC_Exe_Demo(void)
{
  GPIO_InitTypeDef gpio_init;

  LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);/* 开启GPIO的APB时钟 */
  LL_SYSC_ApbClkCtrl(LL_SYSC_INTC_ID, ERN_ENABLE);/* 开启INTC的总APB时钟 */
  LL_SYSC_ApbClkCtrl(BSP_SYSC_INTC_ID, ERN_ENABLE);/* 开启对应INTx的APB时钟 */

  /* 外部INTC中断输入管脚配置 */
  gpio_init.Pin = BSP_INTC_IO;
  gpio_init.Dir = GPIO_MODE_IN;
  gpio_init.InputMode = COMS_MODE;
  gpio_init.Mode = _INT;
  gpio_init.OutputLevel = High_Level;
  gpio_init.OutputMode = PushPll_MODE;
  gpio_init.Pull = Pull_OFF;
  LL_GPIO_Init(&gpio_init);
  /* INTC初始化配置 */
  LL_INTC_Init(BSP_INTC_MODE, BSP_INTC_ID, INTC_RISINGEDGE, INTC_IRQ_ENABLE);

  /* LED1翻转指示，不是INTC使用必须 */
  gpio_init.Pin = PIN_0_0;
  gpio_init.Dir = GPIO_MODE_OUT;
  gpio_init.InputMode = COMS_MODE;
  gpio_init.Mode = _NORMALIO;
  gpio_init.OutputLevel = Low_Level;
  gpio_init.OutputMode = PushPll_MODE;
  gpio_init.Pull = Pull_OFF;
  LL_GPIO_Init(&gpio_init);

}
#endif
