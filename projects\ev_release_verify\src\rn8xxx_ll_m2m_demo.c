#include "main.h"
#ifdef LL_M2M_DEMO
#include "rn8xxx_ll_m2m_demo.h"


void LL_M2M_Exe_Demo(void)
{
    ErrorStatus status;
  static uint32_t addr_src[100] , addr_dec[100],test_data = 0;
  //static uint32 addr_s = (uint32)&addr_src[0];
  //static uint32 addr_d = (uint32)&addr_dec[0];
  for(uint16_t i = 0 ; i < 100 ; i++)
  {
    addr_src[i] = test_data++;
  }
  SYSCTL_ENABLE_WRITE;
  LL_SYSC_ApbClkCtrl(LL_SYSC_M2M_ID,ERN_ENABLE);
  SYSCTL_DISABLE_WRITE;
  
  sll_M2M_InitTypeDef move_para;
  move_para.Ctrl.bitM2MMode.IVLD    = IVLD_3BYTE;
  move_para.Ctrl.bitM2MMode.OVLD    = OVLD_2BYTE;
  move_para.Ctrl.bitM2MMode.ADDR_RVEN = ADDR_RV_EN;
  move_para.Ctrl.bitM2MMode.DUMMY_EN   = DUMMY_EN;
  move_para.Ctrl.bitM2MMode.ORV_EN     = ORV_DIS;
  move_para.SAddr = (uint32_t)&(addr_src[0]);
  move_para.DAddr = (uint32_t)&addr_dec[0];
  move_para.TxLen = 100;
  status = LL_M2M_Move(&move_para);
    
}
#endif
