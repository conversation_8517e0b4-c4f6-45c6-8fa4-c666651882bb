/******************************************************************************
 * @file     rn8xxx_ll_rtc_lib.c
 * @brief    rtc driver
 * <AUTHOR> Technology
 *
 * @note
 * Copyright (C) 2008, Renergy Technology Inc. All rights reserved.
 ******************************************************************************/
#include "rn8xxx_ll_utils.h"
#include "rn8xxx_ll_rtc_lib.h"
#include "rn8xxx_ll_sysoption.h"

static void RtcWait(void);
static void RtcCalWait(void);

static void RtcWait(void)
{
    unsigned int i;
    for (i = 0U; i < 2000U; i++)
    {
        if (((MEM32(RTC_CTRL_ADDR) >> 9) & 0x1U) == 0x0U)
        {
            break;
        }
    }
}

static void RtcCalWait(void)
{
    unsigned int i;

    for (i = 0U; i < 20000U; i++)
    {
        if (((MEM32(RTC_CTRL_ADDR) >> 10) & 0x1U) == 0x0U)
        {
            break;
        }
    }
}

/**
 * @brief LL_RTC_ReadDota
 *
 * @param
 *    none
 * @return
 *    unsigned short dota: dota value of RTC
 *
 */
unsigned short LL_RTC_ReadDota(void)
{
    unsigned short i;
    unsigned int sys_ps;

#if defined(RN_CM0_PLATFORM)
    rn_chipid_t chipid;

    chipid = getChipid();
    sys_ps = MEM32(SYS_PS_ADDR(chipid));
    MEM32(SYS_PS_ADDR(chipid)) = 0x82U;
    MEM32(MOD1_EN_ADDR(chipid)) |= ((unsigned int)1U << 10);
#elif (defined(RN202x_RN7326_SOC_V2) ||\
       defined(RN202x_RN7326_SOC_B))
    sys_ps = MEM32(SYS_PS_9303_ADDR);
    MEM32(SYS_PS_9303_ADDR) = 0x82U;
    MEM32(MOD1_EN_9303_ADDR) |= ((unsigned int)1U << 10);
#else
    sys_ps = MEM32(SYS_PS_9103_ADDR);
    MEM32(SYS_PS_9103_ADDR) = 0x82U;
    MEM32(MOD1_EN_9103_ADDR) |= ((unsigned int)1U << 10);
#endif

    i = (unsigned short)MEM32(RTC_DOTA_ADDR);

    /* FEED_WDT; */

#if defined(RN_CM0_PLATFORM)
    MEM32(SYS_PS_ADDR(chipid)) = sys_ps;
#elif (defined(RN202x_RN7326_SOC_V2) ||\
       defined(RN202x_RN7326_SOC_B))
    MEM32(SYS_PS_9303_ADDR) = sys_ps;
#else
    MEM32(SYS_PS_9103_ADDR) = sys_ps;
#endif

    return i;
}

/* unsigned char LL_SYSOPTION_SysConfCheck(void); */
/**
 * @brief LL_RTC_WriteDota
 *
 * @param
 *    in unsigned short dota: dota value of RTC
 * @return
 *    none
 *
 */
eRtcRet_TypeDef LL_RTC_WriteDota(unsigned short dota)
{
    unsigned int sys_ps, rtc_ctrl, rtcps_bak;
    eRtcRet_TypeDef res;
#if defined(RN_CM0_PLATFORM)
    const rn_chipid_t chipid = getChipid();
    sys_ps = MEM32(SYS_PS_ADDR(chipid));
    MEM32(SYS_PS_ADDR(chipid)) = 0x82U;
    MEM32(MOD1_EN_ADDR(chipid)) |= ((unsigned int)1U << 10);
    /* MEM32(MOD1_EN_ADDR(chipid)) |= ((unsigned int)1U << 9); */ /* WDT APB */
#elif (defined(RN202x_RN7326_SOC_V2) ||\
       defined(RN202x_RN7326_SOC_B))
    sys_ps = MEM32(SYS_PS_9303_ADDR);
    MEM32(SYS_PS_9303_ADDR) = 0x82U;
    MEM32(MOD1_EN_9303_ADDR) |= ((unsigned int)1U << 10);
#else
    sys_ps = MEM32(SYS_PS_9103_ADDR);
    MEM32(SYS_PS_9103_ADDR) = 0x82U;
    MEM32(MOD1_EN_9103_ADDR) |= ((unsigned int)1U << 10);
#endif

    rtc_ctrl = MEM32(RTC_CTRL_ADDR);

    RtcWait();
    MEM32(RTC_CTRL_ADDR) |= 0x100U;

    RtcWait();
    rtcps_bak = MEM32(RTC_PS_ADDR);
    MEM32(RTC_PS_ADDR) = 0x8EU;

    res = (eRtcRet_TypeDef)LL_SYSOPTION_SysConfCheck();

    RtcWait();
    if (0x400U == (MEM32(RTC_CTRL_ADDR) & 0x400U))
    {
        RtcCalWait();
    }
    MEM32(RTC_DOTA_ADDR) = dota;
    if (0x400U == (MEM32(RTC_CTRL_ADDR) & 0x400U))
    {
        RtcCalWait();
        MEM32(RTC_DOTA_ADDR) = dota;
    }

    if (MEM32(RTC_DOTA_ADDR) != dota)
    /* if (RtcCalCheck(chipid)) */
    {
        res = RTC_FAIL;
    }

    RtcWait();
    MEM32(RTC_CTRL_ADDR) = rtc_ctrl;

    RtcWait();
    MEM32(RTC_PS_ADDR) = rtcps_bak;
    RtcWait();

    /* FEED_WDT; */

#if defined(RN_CM0_PLATFORM)
    MEM32(SYS_PS_ADDR(chipid)) = sys_ps;
#elif (defined(RN202x_RN7326_SOC_V2) ||\
       defined(RN202x_RN7326_SOC_B))
    MEM32(SYS_PS_9303_ADDR) = sys_ps;
#else
    MEM32(SYS_PS_9103_ADDR) = sys_ps;
#endif
    return res;
}
/* r2041 */
