/**
 *******************************************************************************
 * @file  template/source/main.c
 * @brief Main program template for the Device Driver Library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-09-08       XT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "main.h"
#ifdef LL_GPIO_DEMO
/**
 * @addtogroup HC32F460_DDL_Examples
 * @{
 */

/**
 * @addtogroup LL_Templates
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 * @brief  Main function of template project
 * @param  None
 * @retval int32_t return value, if needed
 */

#include "rn8xxx_ll.h"
#include "SEGGER_RTT_Conf.h"
#include "SEGGER_RTT.h"

const char gpio_name_str[][sizeof("PIN_14_7 ")]={
    "PIN_0_0  ",     /*"0   	PIN_0_Group + 0x00	"*/
    "PIN_0_1  ",     /*"1   	PIN_0_Group + 0x01	"*/
    "PIN_0_2  ",     /*"2   	PIN_0_Group + 0x02	"*/
    "PIN_0_3  ",     /*"3   	PIN_0_Group + 0x03	"*/
    "PIN_0_4  ",     /*"4   	PIN_0_Group + 0x04	"*/
    "PIN_0_5  ",     /*"5   	PIN_0_Group + 0x05	"*/
    "PIN_0_6  ",     /*"6   	PIN_0_Group + 0x06	"*/
    "PIN_0_7  ",     /*"7   	PIN_0_Group + 0x07	"*/
    "PIN_1_0  ",     /*"8   	PIN_1_Group + 0x00	"*/
    "PIN_1_1  ",     /*"9   	PIN_1_Group + 0x01	"*/
    "PIN_1_2  ",     /*"10  	PIN_1_Group + 0x02	"*/
    "PIN_1_3  ",     /*"11  	PIN_1_Group + 0x03	"*/
    "PIN_1_4  ",     /*"12  	PIN_1_Group + 0x04	"*/
    "PIN_1_5  ",     /*"13  	PIN_1_Group + 0x05	"*/
    "PIN_1_6  ",     /*"14  	PIN_1_Group + 0x06	"*/
    "PIN_1_7  ",     /*"15  	PIN_1_Group + 0x07	"*/
    "PIN_2_0  ",     /*"16  	PIN_2_Group + 0x00	"*/
    "PIN_2_1  ",     /*"17  	PIN_2_Group + 0x01	"*/
    "PIN_2_2  ",     /*"18  	PIN_2_Group + 0x02	"*/
    "PIN_2_3  ",     /*"19  	PIN_2_Group + 0x03	"*/
    "PIN_2_4  ",     /*"20  	PIN_2_Group + 0x04	"*/
    "PIN_2_5  ",     /*"21  	PIN_2_Group + 0x05	"*/
    "PIN_2_6  ",     /*"22  	PIN_2_Group + 0x06	"*/
    "PIN_2_7  ",     /*"23  	PIN_2_Group + 0x07	"*/
    "PIN_3_0  ",     /*"24  	PIN_3_Group + 0x00	"*/
    "PIN_3_1  ",     /*"25  	PIN_3_Group + 0x01	"*/
    "PIN_3_2  ",     /*"26  	PIN_3_Group + 0x02	"*/
    "PIN_3_3  ",     /*"27  	PIN_3_Group + 0x03	"*/
    "PIN_3_4  ",     /*"28  	PIN_3_Group + 0x04	"*/
    "PIN_3_5  ",     /*"29  	PIN_3_Group + 0x05	"*/
    "PIN_3_6  ",     /*"30  	PIN_3_Group + 0x06	"*/
    "PIN_3_7  ",     /*"31  	PIN_3_Group + 0x07	"*/
    "PIN_4_0  ",     /*"32  	PIN_4_Group + 0x00	"*/
    "PIN_4_1  ",     /*"33  	PIN_4_Group + 0x01	"*/
    "PIN_4_2  ",     /*"34  	PIN_4_Group + 0x02	"*/
    "PIN_4_3  ",     /*"35  	PIN_4_Group + 0x03	"*/
    "PIN_4_4  ",     /*"36  	PIN_4_Group + 0x04	"*/
    "PIN_4_5  ",     /*"37  	PIN_4_Group + 0x05	"*/
    "PIN_4_6  ",     /*"38  	PIN_4_Group + 0x06	"*/
    "PIN_4_7  ",     /*"39  	PIN_4_Group + 0x07	"*/
    "PIN_5_0  ",     /*"40  	PIN_5_Group + 0x00	"*/
    "PIN_5_1  ",     /*"41  	PIN_5_Group + 0x01	"*/
    "PIN_5_2  ",     /*"42  	PIN_5_Group + 0x02	"*/
    "PIN_5_3  ",     /*"43  	PIN_5_Group + 0x03	"*/
    "PIN_5_4  ",     /*"44  	PIN_5_Group + 0x04	"*/
    "PIN_5_5  ",     /*"45  	PIN_5_Group + 0x05	"*/
    "PIN_5_6  ",     /*"46  	PIN_5_Group + 0x06 	��VCC�޵磬VBAT�����ϵ�ʱ��P56Ĭ�����1HZ��"*/
    "PIN_5_7  ",     /*"47  	PIN_5_Group + 0x07	"*/
    "PIN_6_0  ",     /*"48  	PIN_6_Group + 0x00	"*/
    "PIN_6_1  ",     /*"49  	PIN_6_Group + 0x01	"*/
    "PIN_6_2  ",     /*"50  	PIN_6_Group + 0x02	"*/
    "PIN_6_3  ",     /*"51  	PIN_6_Group + 0x03	"*/
    "PIN_6_4  ",     /*"52  	PIN_6_Group + 0x04	"*/
    "PIN_6_5  ",     /*"53  	PIN_6_Group + 0x05	"*/
    "PIN_6_6  ",     /*"54  	PIN_6_Group + 0x06	"*/
    "PIN_6_7  ",     /*"55  	PIN_6_Group + 0x07	"*/
    "PIN_7_0  ",     /*"56  	PIN_7_Group + 0x00	"*/
    "PIN_7_1  ",     /*"57  	PIN_7_Group + 0x01	"*/
    "PIN_7_2  ",     /*"58  	PIN_7_Group + 0x02	"*/
    "PIN_7_3  ",     /*"59  	PIN_7_Group + 0x03	"*/
    "PIN_7_4  ",     /*"60  	PIN_7_Group + 0x04	"*/
    "PIN_7_5  ",     /*"61  	PIN_7_Group + 0x05	"*/
    "PIN_7_6  ",     /*"62  	PIN_7_Group + 0x06	"*/
    "PIN_7_7  ",     /*"63  	PIN_7_Group + 0x07	"*/
    "PIN_8_0  ",     /*"64  	PIN_8_Group + 0x00	"*/
    "PIN_8_1  ",     /*"65  	PIN_8_Group + 0x01	"*/
    "PIN_8_2  ",     /*"66  	PIN_8_Group + 0x02	"*/
    "PIN_8_3  ",     /*"67  	PIN_8_Group + 0x03	"*/
    "PIN_8_4  ",     /*"68  	PIN_8_Group + 0x04	"*/
    "PIN_8_5  ",     /*"69  	PIN_8_Group + 0x05	"*/
    "PIN_8_6  ",     /*"70  	PIN_8_Group + 0x06	"*/
    "PIN_8_7  ",     /*"71  	PIN_8_Group + 0x07	"*/
    "PIN_9_0  ",     /*"72  	PIN_9_Group + 0x00	"*/
    "PIN_9_1  ",     /*"73  	PIN_9_Group + 0x01	"*/
    "PIN_9_2  ",     /*"74  	PIN_9_Group + 0x02	"*/
    "PIN_9_3  ",     /*"75  	PIN_9_Group + 0x03	"*/
    "PIN_9_4  ",     /*"76  	PIN_9_Group + 0x04	"*/
    "PIN_9_5  ",     /*"77  	PIN_9_Group + 0x05	"*/
    "PIN_9_6  ",     /*"78  	PIN_9_Group + 0x06	"*/
    "PIN_9_7  ",     /*"79  	PIN_9_Group + 0x07	"*/
    "PIN_10_0 ",     /*" 80 	PIN_10_Group + 0x00	"*/
    "PIN_10_1 ",     /*" 81 	PIN_10_Group + 0x01	"*/
    "PIN_10_2 ",     /*" 82 	PIN_10_Group + 0x02	"*/
    "PIN_10_3 ",     /*" 83 	PIN_10_Group + 0x03	"*/
    "PIN_10_4 ",     /*" 84 	PIN_10_Group + 0x04	"*/
    "PIN_10_5 ",     /*" 85 	PIN_10_Group + 0x05	"*/
    "PIN_10_6 ",     /*" 86 	PIN_10_Group + 0x06	"*/
    "PIN_10_7 ",     /*" 87 	PIN_10_Group + 0x07	"*/
    "PIN_11_0 ",     /*" 88 	PIN_11_Group + 0x00	"*/
    "PIN_11_1 ",     /*" 89 	PIN_11_Group + 0x01	"*/
    "PIN_11_2 ",     /*" 90 	PIN_11_Group + 0x02	"*/
    "PIN_11_3 ",     /*" 91 	PIN_11_Group + 0x03	"*/
    "PIN_11_4 ",     /*" 92 	PIN_11_Group + 0x04	"*/
    "PIN_11_5 ",     /*" 93 	PIN_11_Group + 0x05	"*/
    "PIN_11_6 ",     /*" 94 	PIN_11_Group + 0x06	"*/
    "PIN_11_7 ",     /*" 95 	PIN_11_Group + 0x07	"*/
    "PIN_12_0 ",     /*" 96 	PIN_12_Group + 0x00	"*/
    "PIN_12_1 ",     /*" 97 	PIN_12_Group + 0x01	"*/
    "PIN_12_2 ",     /*" 98 	PIN_12_Group + 0x02	"*/
    "PIN_12_3 ",     /*" 99 	PIN_12_Group + 0x03	"*/
    "PIN_12_4 ",     /*" 100	PIN_12_Group + 0x04	"*/
    "PIN_12_5 ",     /*" 101	PIN_12_Group + 0x05	"*/
    "PIN_12_6 ",     /*" 102	PIN_12_Group + 0x06	"*/
    "PIN_12_7 ",     /*" 103	PIN_12_Group + 0x07	"*/
    "PIN_13_0 ",     /*" 104	PIN_13_Group + 0x00	"*/
    "PIN_13_1 ",     /*" 105	PIN_13_Group + 0x01	"*/
    "PIN_13_2 ",     /*" 106	PIN_13_Group + 0x02	"*/
    "PIN_13_3 ",     /*" 107	PIN_13_Group + 0x03	"*/
    "PIN_13_4 ",     /*" 108	PIN_13_Group + 0x04	"*/
    "PIN_13_5 ",     /*" 109	PIN_13_Group + 0x05	"*/
    "PIN_13_6 ",     /*" 110	PIN_13_Group + 0x06	"*/
    "PIN_13_7 ",     /*" 111	PIN_13_Group + 0x07	"*/
    "PIN_14_0 ",     /*" 112	PIN_14_Group + 0x00	"*/
    "PIN_14_1 ",     /*" 113	PIN_14_Group + 0x01	"*/
    "PIN_14_2 ",     /*" 114	PIN_14_Group + 0x02	"*/
    "PIN_14_3 ",     /*" 115	PIN_14_Group + 0x03	"*/
    "PIN_14_4 ",     /*" 116	PIN_14_Group + 0x04	"*/
    "PIN_14_5 ",     /*" 117	PIN_14_Group + 0x05	"*/
    "PIN_14_6 ",     /*" 118	PIN_14_Group + 0x06	"*/
    "PIN_14_7 ",     /*" 119	PIN_14_Group + 0x07	"*/
};
const char gpio_fun_str[][20]={
    "_NORMALIO = 1U",
    "_HOSCI", /*高频晶体管脚*/
    "_HOSCO" ,/*高频晶体管脚*/
    "_SWD",
    "_UART0", /*"TX,RX组合同步设置"*/
    "_UART1",
    "_UART2",
    "_UART3",
    "_UART4",
    "_UART5",
    "_UART6",
    "_UART7",
    "_LPUART",
    "_CAN",
    "_CAN_RX",
    "_CAN_TX",

    "_IIC",/*"芯片只有一个IIC时选择,_IIC0,"*/
    "_IIC0",
    "_IIC1",
    "_SPI",/*RN202x_RN7326_SOC_V2使用，具体SPI号需要另外调用函数*/  
    "_SPI0",
    "_SPI1",
    "_SPI2",
    "_SPI3",
    "_SPI4",
    "_ISO7816",

    "_TC_IN",
    "_TC0_N_0", /*"TCO的0通道的反向输出"*/
    "_TC0_P_0", /*"TCO的0通道的正向输出"*/
    "_TC0_N_1", /*"TCO的1通道的反向输出"*/
    "_TC0_P_1", /*"TCO的1通道的正向输出"*/
    "_TC1_N_0", /*"TC1的0通道的反向输出"*/
    "_TC1_P_0",
    "_TC1_N_1",
    "_TC1_P_1",

    "_RTC_OUT",
    "_RTC_1S",

    "_KEY",
    "_KEY_OUT",

    "_INT",

    /*"*******************计量*******************"*/
    "_SF_OUT", /*"视在电能脉冲输出，脉冲转发功能"*/
    "_SF",     /*""*/
    "_PF",     /*"有功电能脉冲输出，脉冲转发功能"*/
    "_QF",     /*"无功电能脉冲输出，脉冲转发功能"*/
    "_SF1",    /*"脉冲转发功能，INT6口输入，SF1输出"*/
    "_SF2",    /*"脉冲转发功能，INT7口输入，SF2输出"*/
    "_CF0",    /*"CF0"*/
    "_CF1",    /*"CF1"*/
    "_CF2",    /*"CF2"*/
    "_CF3",    /*"CF3"*/
    "_CF4",    /*"CF4"*/
    "_CF_OUT0",/*"电能脉冲"*/
    "_CF_OUT1",
    "_CF_OUT2",
    "_CF_OUT3",
    "_CF_OUT4",
    "_D2F_OUT0",
    "_D2F_OUT1",
    "_D2F_OUT2",
    "_D2F_OUT3",
    "_D2F_OUT4",
    "_IOCNT_OUT0",/*"脉冲转发"*/
    "_IOCNT_OUT1",
    "_IOCNT_OUT2",
    "_IOCNT_OUT3",
    "_IOCNT_OUT4",

    "_ZXOUT",
    /*"*******************模拟*******************"*/
    "_AIN", /*"SDAIN,AIN "*/
    "_CMP2",
    "_LVD",
    "_CTSWO",
    "_CTCMPA",
    "_CTCMPB",
    "_CTCMPC",

    "_LCDV", /*"gpio配置LCDVA, LCDVB, LCDVC, LCDVD, LCDVP1, LCDVP2"*/
    "_SEG",  /*"SEG/COM配置为SEG"*/
    "_COM",  /*"SEG/COM配置为COM"*/

    "_ADC_CLKO",
    "_IA_IN",
    "_IB_IN",
    "_IC_IN",
    "_U_IN",
    "_TRIG_OUT",

};

typedef struct
{
    uint32_t *RegAd;
    uint32_t def_data;
    char fun_reg_name[15];
    // uint8_t					nbit;
} sGPIORegDef_TypeDef; /*"GPIO寄存器"*/
const sGPIORegDef_TypeDef GPIORegDef[] = {
#ifdef RN202x_RN7326_SOC_V2
    {(uint32_t *)&GPIO->PMA, 0xFFFFFF3F, "GPIO->PMA     "}, /* 0x00 0xFFFFFF1F*/
#elif defined(RN821x_RN721x_SOC_V2)
    {(uint32_t *)&GPIO->PMA, 0xFFFFFF3F, "GPIO->PMA     "}, /* 0x00 0xFFFFFF1F*/
    {(uint32_t *)&GPIO->PCA0, 0x20000000, "GPIO->PCA0    "}, /* 0x08 */
    {(uint32_t *)&GPIO->PCA1, 0x00000020, "GPIO->PCA1    "}, /* 0x0C */
    {(uint32_t *)&GPIO->PUA, 0x01300000, "GPIO->PUA     "},  /* 0x10 */
    {(uint32_t *)&GPIO->PIMA, 0x00000000, "GPIO->PIMA    "}, /* 0x14 */
    {(uint32_t *)&GPIO->PIEA, 0xFEFFFF3F, "GPIO->PIEA    "}, /* 0x18 */
    {(uint32_t *)&GPIO->PMB, 0xFFFFFFFF, "GPIO->PMB     "},  /* 0x1C */
    {(uint32_t *)&GPIO->PCB, 0x00000300, "GPIO->PCB     "},  /* 0x24 */
    {(uint32_t *)&GPIO->PUB, 0x00000000, "GPIO->PUB     "},  /* 0x28 */
    {(uint32_t *)&GPIO->PIMB, 0x00000000, "GPIO->PIMB    "}, /* 0x2C */
    {(uint32_t *)&GPIO->PIEB, 0xFFFFFFFF, "GPIO->PIEB    "}, /* 0x30 */
    {(uint32_t *)&GPIO->PMC, 0xFFFFFFFF, "GPIO->PMC     "},  /* 0x34 */
    {(uint32_t *)&GPIO->PCC, 0x00000000, "GPIO->PCC     "},  /* 0x3C */
    {(uint32_t *)&GPIO->PUC, 0x00000000, "GPIO->PUC     "},  /* 0x40 */
    {(uint32_t *)&GPIO->PIEC, 0xFFFFFFFF, "GPIO->PIEC    "}, /* 0x44 */
    {(uint32_t *)&GPIO->PIMC, 0x00000000, "GPIO->PIMC    "}, /* 0x48 */
    {(uint32_t *)&GPIO->PCB2, 0x00000000, "GPIO->PCB2    "}, /* 0x4C */
    {(uint32_t *)&GPIO->PMD, 0x00FFFFFF, "GPIO->PMD     "},  /* 0x50 */
    {(uint32_t *)&GPIO->PCD, 0x00003FFF, "GPIO->PCD     "}, /* 0x58 */
    {(uint32_t *)&GPIO->PUD, 0x00000000, "GPIO->PUD     "}, /* 0x5C */
    {(uint32_t *)&GPIO->PCE, 0x00000000, "GPIO->PCE     "}, /* 0x60 */
    {(uint32_t *)&GPIO->PIED, 0x00FFFFFF, "GPIO->PIED    "},     /* 0x84 */
    {(uint32_t *)&GPIO->PIMD, 0x00000000, "GPIO->PIMD    "},     /* 0x88 */
    {(uint32_t *)&GPIO->PCA2, 0x00000000, "GPIO->PCA2    "},     /* 0x8C */
    {(uint32_t *)&GPIO->PCA3, 0x00000000, "GPIO->PCA3    "},     /* 0x90 */
    {(uint32_t *)&GPIO->PCB3, 0x00000000, "GPIO->PCB3    "},     /* 0x94 */
    {(uint32_t *)&GPIO->PCC2, 0x00000000, "GPIO->PCC2    "},     /* 0x98 */
    {(uint32_t *)&GPIO->PCC3, 0x00000000, "GPIO->PCC3    "},     /* 0x9C */
    {(uint32_t *)&GPIO->PCC4, 0x00000000, "GPIO->PCC4    "},     /* 0xA0 */
    {(uint32_t *)&GPIO->PCD2, 0x00000000, "GPIO->PCD2    "},     /* 0xA8 */
    {(uint32_t *)&GPIO->PIMA2, 0x00000000, "GPIO->PIMA2   "},    /* 0xB0 */
    {(uint32_t *)&GPIO->LURT_CFG, 0x00000000, "GPIO->LURT_CFG"}, /* 0x100 */
    {(uint32_t *)&GPIO->IOCFG, 0x0FFFFFFF, "GPIO->IOCFG   "},    /* 0x104 */
#elif defined(RN821x_RN721x_SOC_V3)
   {(uint32_t *)&GPIO->PMA, 0xFFFFFF3F, "GPIO->PMA     "}, /* 0x00 0xFFFFFF1F*/
    {(uint32_t *)&GPIO->PCA0, 0x20000000, "GPIO->PCA0    "}, /* 0x08 */
    {(uint32_t *)&GPIO->PCA1, 0x00000020, "GPIO->PCA1    "}, /* 0x0C */
    {(uint32_t *)&GPIO->PUA, 0x01300000, "GPIO->PUA     "},  /* 0x10 */
    {(uint32_t *)&GPIO->PIMA, 0x00000000, "GPIO->PIMA    "}, /* 0x14 */
    {(uint32_t *)&GPIO->PIEA, 0xFEFFFF3F, "GPIO->PIEA    "}, /* 0x18 */
    {(uint32_t *)&GPIO->PMB, 0xFFFFFFFF, "GPIO->PMB     "},  /* 0x1C */
    {(uint32_t *)&GPIO->PCB, 0x00000300, "GPIO->PCB     "},  /* 0x24 */
    {(uint32_t *)&GPIO->PUB, 0x00000000, "GPIO->PUB     "},  /* 0x28 */
    {(uint32_t *)&GPIO->PIMB, 0x00000000, "GPIO->PIMB    "}, /* 0x2C */
    {(uint32_t *)&GPIO->PIEB, 0xFFFFFFFF, "GPIO->PIEB    "}, /* 0x30 */
    {(uint32_t *)&GPIO->PMC, 0xFFFFFFFF, "GPIO->PMC     "},  /* 0x34 */
    {(uint32_t *)&GPIO->PCC, 0x00000000, "GPIO->PCC     "},  /* 0x3C */
    {(uint32_t *)&GPIO->PUC, 0x00000000, "GPIO->PUC     "},  /* 0x40 */
    {(uint32_t *)&GPIO->PIEC, 0xFFFFFFFF, "GPIO->PIEC    "}, /* 0x44 */
    {(uint32_t *)&GPIO->PIMC, 0x00000000, "GPIO->PIMC    "}, /* 0x48 */
    {(uint32_t *)&GPIO->PCB2, 0x00000000, "GPIO->PCB2    "}, /* 0x4C */
    {(uint32_t *)&GPIO->PMD, 0x00FFFFFF, "GPIO->PMD     "},  /* 0x50 */
    {(uint32_t *)&GPIO->PCD, 0x00003FFF, "GPIO->PCD     "}, /* 0x58 */
    {(uint32_t *)&GPIO->PUD, 0x00000000, "GPIO->PUD     "}, /* 0x5C */
    {(uint32_t *)&GPIO->PCE, 0x00000000, "GPIO->PCE     "}, /* 0x60 */
    {(uint32_t *)&GPIO->PIED, 0x00FFFFFF, "GPIO->PIED    "},     /* 0x84 */
    {(uint32_t *)&GPIO->PIMD, 0x00000000, "GPIO->PIMD    "},     /* 0x88 */
    {(uint32_t *)&GPIO->PCA2, 0x00000000, "GPIO->PCA2    "},     /* 0x8C */
    {(uint32_t *)&GPIO->PCA3, 0x00000000, "GPIO->PCA3    "},     /* 0x90 */
    {(uint32_t *)&GPIO->PCB3, 0x00000000, "GPIO->PCB3    "},     /* 0x94 */
    {(uint32_t *)&GPIO->PCC2, 0x00000000, "GPIO->PCC2    "},     /* 0x98 */
    {(uint32_t *)&GPIO->PCC3, 0x00000000, "GPIO->PCC3    "},     /* 0x9C */
    {(uint32_t *)&GPIO->PCC4, 0x00000000, "GPIO->PCC4    "},     /* 0xA0 */
    {(uint32_t *)&GPIO->PCD2, 0x00000000, "GPIO->PCD2    "},     /* 0xA8 */
    {(uint32_t *)&GPIO->PIMA2, 0x00000000, "GPIO->PIMA2   "},    /* 0xB0 */
    {(uint32_t *)&GPIO->IOCFG, 0x0FFFFFFF, "GPIO->IOCFG   "},    /* 0x104 */   
#elif defined(RN831x_RN861x_MCU_V2) || defined(RN831x_RN861x_MCU_V3)
    {(uint32_t *)&GPIO->PMA, 0xFFFFFF3F, "GPIO->PMA     "},  /* 0x00 0xFFFFFF1F*/
    {(uint32_t *)&GPIO->PCA0, 0x20000000, "GPIO->PCA0    "}, /* 0x08 */
    {(uint32_t *)&GPIO->PCA1, 0x00000020, "GPIO->PCA1    "}, /* 0x0C */
    {(uint32_t *)&GPIO->PUA, 0x01300000, "GPIO->PUA     "},  /* 0x10 */
    {(uint32_t *)&GPIO->PIMA, 0x00000000, "GPIO->PIMA    "}, /* 0x14 */
    {(uint32_t *)&GPIO->PIEA, 0xFEFFFF3F, "GPIO->PIEA    "}, /* 0x18 */
    {(uint32_t *)&GPIO->PMB, 0xFFFFFFFF, "GPIO->PMB     "},  /* 0x1C */
    {(uint32_t *)&GPIO->PCB, 0x00000300, "GPIO->PCB     "},  /* 0x24 */
    {(uint32_t *)&GPIO->PUB, 0x00000000, "GPIO->PUB     "},  /* 0x28 */
    {(uint32_t *)&GPIO->PIMB, 0x00000000, "GPIO->PIMB    "}, /* 0x2C */
    {(uint32_t *)&GPIO->PIEB, 0xFFFFFFFF, "GPIO->PIEB    "}, /* 0x30 */
    {(uint32_t *)&GPIO->PMC, 0xFFFFFFFF, "GPIO->PMC     "},  /* 0x34 */
    {(uint32_t *)&GPIO->PCC, 0x00000000, "GPIO->PCC     "},  /* 0x3C */
    {(uint32_t *)&GPIO->PUC, 0x00000000, "GPIO->PUC     "},  /* 0x40 */
    {(uint32_t *)&GPIO->PIEC, 0xFFFFFFFF, "GPIO->PIEC    "}, /* 0x44 */
    {(uint32_t *)&GPIO->PIMC, 0x00000000, "GPIO->PIMC    "}, /* 0x48 */
    {(uint32_t *)&GPIO->PCB2, 0x00000000, "GPIO->PCB2    "}, /* 0x4C */
    {(uint32_t *)&GPIO->PMD, 0x00FFFFFF, "GPIO->PMD     "},  /* 0x50 */

    {(uint32_t *)&GPIO->PCD, 0x00003FFF, "GPIO->PCD     "}, /* 0x58 */
    {(uint32_t *)&GPIO->PUD, 0x00000000, "GPIO->PUD     "}, /* 0x5C */
    {(uint32_t *)&GPIO->PCE, 0x00000000, "GPIO->PCE     "}, /* 0x60 */

    {(uint32_t *)&GPIO->PIED, 0x00FFFFFF, "GPIO->PIED    "},     /* 0x84 */
    {(uint32_t *)&GPIO->PIMD, 0x00000000, "GPIO->PIMD    "},     /* 0x88 */

#endif
};

void delay(uint32_t delayms)
{
    uint32_t i ,j;
    for(i=0;i<delayms;i++)
    {
        for(j=0;j<500;j++)
        {
            __NOP();__NOP();__NOP();__NOP();__NOP();
        }
    }
}

void show_reg_diff(void)
{
    uint32_t i ,j;
    uint32_t reg_data,def_data,different;
    for(i = 0;i < (sizeof(GPIORegDef)/sizeof(GPIORegDef[0]));i++)
    {
        reg_data = *(GPIORegDef[i].RegAd);
        def_data = GPIORegDef[i].def_data;
        if(reg_data != def_data)
        {
            SEGGER_RTT_printf(0, "                         %s:reg is:0x%08x;def is:0x%08x;",\
                                    GPIORegDef[i].fun_reg_name,reg_data,def_data);
            delay(1);
            different = reg_data^def_data;
            for(j=0;j<32;j++)
            {
                if(different&(1l<<j))
                {
                    SEGGER_RTT_printf(0, "bit_%d;",j);   
                    delay(1);
                }
            }
            SEGGER_RTT_printf(0, "\r\n");
            delay(1);
        }
        

    }
}

void reg_to_def(void)
{
    uint32_t i ;
    uint32_t reg_data,def_data;
    for(i = 0;i < (sizeof(GPIORegDef)/sizeof(GPIORegDef[0]));i++)
    {
        reg_data = *(GPIORegDef[i].RegAd);
        def_data = GPIORegDef[i].def_data;
        if(reg_data != def_data)
        {
            *(GPIORegDef[i].RegAd) = def_data;
        }
        

    }
}

void show_Level(uint32_t pin_id)
{
    uint8_t Level;
    Level = LL_GPIO_ReadPin(pin_id);
    if(0 == Level)
    {
        SEGGER_RTT_printf(0, "%s is low\r\n",gpio_name_str[pin_id]);
    }
    else
    {
        SEGGER_RTT_printf(0, "%s is high\r\n",gpio_name_str[pin_id]);
    }   
}

void LL_GPIO_Exe_Demo(void)
{
    uint32_t pin_id;
    eGPIOFunction_TypeDef fun_id;
    SEGGER_RTT_Init();
    SEGGER_RTT_printf(0, "SYSCTL->OSC_CTRL1->%x\r\nSYSCTL->SYS_RST->%x\r\n",SYSCTL->OSC_CTRL1,SYSCTL->SYS_RST);
    /* Add your code here */
    LL_SYSCLK_SysModeChg(Clock_PLL_14M7, Clock_Div_1);
    for(pin_id = PIN_0_0;pin_id <= PIN_14_7;pin_id++)
    {
        for(fun_id = _NORMALIO;fun_id <= _TRIG_OUT;fun_id++)
        {
            if((pin_id != PIN_2_4)&&(pin_id != PIN_2_5))
            {
                if(LL_GPIO_CfgFun(pin_id,fun_id) == ERN_SUCCESS)
                {
                    SEGGER_RTT_printf(0, "%s-->%s\r\n",\
                                    gpio_name_str[pin_id],\
                                    gpio_fun_str[fun_id]);
                    show_reg_diff();
                    reg_to_def();
                }
            }
            LL_WDT_ReloadCounter();  
        }     

    }
    /*"推挽输出"*/
    for(pin_id = PIN_0_0;pin_id <= PIN_14_7;pin_id++)
    {
        SEGGER_RTT_printf(0, "%s\r\n",gpio_name_str[pin_id]);
        if((pin_id != PIN_2_4)&&(pin_id != PIN_2_5))
        {
            LL_GPIO_CfgInit(pin_id,_NORMALIO,High_Level,Pull_OFF,GPIO_MODE_OUT,COMS_MODE,PushPll_MODE);
            delay(100);
            LL_GPIO_OverturnPin(pin_id);
            delay(100);
            LL_GPIO_OverturnPin(pin_id);
            delay(100);
            LL_GPIO_OverturnPin(pin_id);
        }
        LL_WDT_ReloadCounter();
    }
    /*"COMS输入"*/
    for(pin_id = PIN_0_0;pin_id <= PIN_14_7;pin_id++)
    {
        if((pin_id != PIN_2_4)&&(pin_id != PIN_2_5))
        {
            LL_GPIO_CfgInit(pin_id,_NORMALIO,Low_Level,Pull_OFF,GPIO_MODE_IN,COMS_MODE,PushPll_MODE);
            delay(10);
            show_Level(pin_id);
            delay(500);
            show_Level(pin_id);
        }
        LL_WDT_ReloadCounter();
    }
    SYSCTL_ENABLE_WRITE;
    
    LL_GPIO_CfgInit(PIN_3_6,_NORMALIO,Low_Level,Pull_OFF,GPIO_MODE_IN,COMS_MODE,PushPll_MODE);
    LL_GPIO_CfgInit(PIN_3_7,_NORMALIO,Low_Level,Pull_OFF,GPIO_MODE_IN,COMS_MODE,PushPll_MODE);

    LL_GPIO_CfgInit(PIN_3_6,_INT,Low_Level,Pull_OFF,GPIO_MODE_IN,COMS_MODE,PushPll_MODE);
    LL_GPIO_CfgInit(PIN_3_7,_INT,Low_Level,Pull_OFF,GPIO_MODE_IN,COMS_MODE,PushPll_MODE);
    //LL_INTC_Init(INTC_ID6, INTC_DOUBLEEDGE, INTC_IRQ_ENABLE);
    //LL_INTC_Init(INTC_ID7, INTC_DOUBLEEDGE, INTC_IRQ_ENABLE);
    while(1) 
    { 

        LL_WDT_ReloadCounter();           
    }
}
#endif
/**
 * @}
 */

/**
 * @}
 */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
