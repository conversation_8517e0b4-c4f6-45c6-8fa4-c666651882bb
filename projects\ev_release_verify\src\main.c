/**
 *******************************************************************************
 * @file  template/source/main.c
 * @brief Main program template for the Device Driver Library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-09-08       XT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Co., Ltd. All rights reserved.
 *

 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/

/**
 * @addtogroup renerg_ll_examples
 * @{
 */

/**
 * @addtogroup LL_Templates
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 * @brief  Main function of template project
 * @param  None
 * @retval int32_t return value, if needed
 */

#include "main.h"

#if defined(RN831x_RN861x_MCU_V3)
const rn_lib_t *rn_lib = (rn_lib_t *)0x40600;
#endif

int main(void)
{
/* Add your code here */
#if defined(LL_LOW_POWER_DEMO)
    LL_Low_Power_Exe_Demo();
#endif

#if defined(RN831x_RN861x_MCU_V3)
    rn_lib->LL_SYSCLK_SysModeChg(Clock_PLL_7M4, Clock_Div_1);
#else
    LL_SYSCLK_SysModeChg(Clock_PLL_7M4, Clock_Div_1);
#endif
    SystemCoreClockUpdate();

#if defined(LL_UART_DEMO)
    LL_UART_Exe_Demo();
#endif

#if defined(LL_TC_DEMO)
    LL_TC_Exe_Demo();
#endif

#if defined(LL_SMTC_DEMO)
    LL_SMTC_Exe_Demo();
#endif

#if defined(LL_GPIO_DEMO)
    LL_GPIO_Exe_Demo();
#endif

#if defined(LL_INTC_DEMO)
    LL_INTC_Exe_Demo();
#endif

#if defined(LL_KBI_DEMO)
    LL_KBI_Exe_Demo();
#endif

#if defined(LL_M2M_DEMO)
    LL_M2M_Exe_Demo();
#endif

#if defined(LL_MADC_DEMO)
    LL_MADC_Exe_Demo();
#endif

#if defined(LL_SYSC_DEMO)
    LL_SYSC_Exe_Demo();
#endif

#if defined(LL_WDT_DEMO)
    LL_WDT_Exe_Demo();
#endif

#if defined(LL_EMU_DEMO)
    LL_EMU_Exe_Demo();
#endif

#if defined(LL_RTC_DEMO)
    LL_RTC_Exe_Demo();
#endif

#if defined(LL_SPI_DEMO)
    LL_SPI_Exe_Demo();
#endif

    while (1)
    {
        LL_WDT_ReloadCounter();
        SystemDelayUs(1000);
    }
}

/**
 * @}
 */

/**
 * @}
 */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
