/********************************************************************************
 * @file    Rn8xxx_Common.h
 * <AUTHOR> Application Team
 * @version V1.1.1
 * @date    2023-09-11
 * @brief   Header file containing functions prototypes of common library.
 ******************************************************************************
 * @attention
 *
 * Copyright (c) 2016 Renergy.
 * All rights reserved.
 *
 * This software is licensed under terms that can be found in the LICENSE file
 * in the root directory of this software component.
 * If no LICENSE file comes with this software, it is provided AS-IS.
 *
 *
 ******************************************************************************
 */
/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef RN8xxx_Config_H_
#define RN8xxx_Config_H_

/* 目前支持如下芯片，请根据实际型号配置为如下宏定义 */
/* RN8213_B/RN8215_B/RN8213B_B/RN7213_B */
/* RN8213_C/RN8211B_C/RN8211_C */
/* RN8213_D/RN8217_D/RN8213B_D/RN7213_D */
/* RN8211B_V2/RN8213_V2/RN8215_V2/RN8217_V2/RN8213B_V2/RN7213_V2/RN7213_V2 */
/* RN8211B_V3/RN8213_V3*/
/* RN8318_V2/RN8615_V2/RN8613_V2/RN8611_V2 */
/* RN8613_V3/RN8610_V3 */
/* RN2025_V2/RN2026_V2/RN2028_V2/RN7326_V2 */
/* #define RN8318_V2*/
//#define RN8318_V2
/*建议在工程内配置系列号，如RN821x_RN721x_SOC_V2；因库函数和CMSIS头文件均使用系列号做预编译选项，如要在文件内定义，需在该处、库函数和CMSIS头文件内分别定义*/
/* 当前ll库支持芯片系列号
  RN821x_RN721x_SOC_D
  RN821x_RN721x_SOC_V2
  RN821x_RN721x_SOC_V3
  RN831x_RN861x_MCU_V2
  RN831x_RN861x_MCU_V3
  RN202x_RN7326_SOC_V2
 */
/* #if defined(RN8213_B) || defined(RN8215_B) || defined(RN8213B_B)|| defined(RN7213_B)
#define RN821x_RN721x_SOC_B
#elif defined(RN8213_C) || defined(RN8211B_C) || defined(RN8211_C)
#define RN821x_RN721x_SOC_C*/
#if defined(RN8213_D) || defined(RN8217_D) || defined(RN8213B_D) || defined(RN7213_D)
#define RN821x_RN721x_SOC_D 
#elif defined(RN8211B_V2) || defined(RN8213_V2) || defined(RN8215_V2) || defined(RN8217_V2) || defined(RN8213B_V2) || defined(RN7213_V2) || defined(RN7213_V2)
#define RN821x_RN721x_SOC_V2 
#elif defined(RN8318_V2) || defined(RN8615_V2) || defined(RN8613_V2)|| defined(RN8611_V2)
#define RN831x_RN861x_MCU_V2
#elif defined(RN8211B_V3) || defined(RN8213_V3)
#define RN821x_RN721x_SOC_V3
#elif defined(RN8613_V3)|| defined(RN8610_V3)
#define RN831x_RN861x_MCU_V3
/* #elif defined(RN2025_B)|| defined(RN2026_B)|| defined(RN7326_B)
#define RN202x_RN7326_SOC_B */
#elif defined(RN2025_V2)|| defined(RN2026_V2)|| defined(RN2028_V2)|| defined(RN7326_V2)
#define RN202x_RN7326_SOC_V2
#endif

#if !defined(RN821x_RN721x_SOC_B) && \
    !defined(RN821x_RN721x_SOC_C) && \
    !defined(RN821x_RN721x_SOC_D) && \
    !defined(RN821x_RN721x_SOC_V2) && \
    !defined(RN821x_RN721x_SOC_V3) && \
    !defined(RN831x_RN861x_MCU_V2) && \
    !defined(RN831x_RN861x_MCU_V3) && \
    !defined(RN202x_RN7326_SOC_B) && \
    !defined(RN202x_RN7326_SOC_V2)
    #error "Your must select one chip version!"
#endif


#define LL_D2F_MODULE_ENABLED

#if defined(RN821x_RN721x_SOC_V2) || \
    defined(RN831x_RN861x_MCU_V2) || \
    defined(RN202x_RN7326_SOC_V2)
#define LL_DSP_MODULE_ENABLED /*MCU_V2 SOC_V2才支持DSP模块*/
#endif

#if defined(RN821x_RN721x_SOC_B) || \
    defined(RN821x_RN721x_SOC_C) || \
    defined(RN821x_RN721x_SOC_D) || \
    defined(RN821x_RN721x_SOC_V2) ||\
    defined(RN821x_RN721x_SOC_V3) ||\
    defined(RN202x_RN7326_SOC_V2)
#define LL_EMU_MODULE_ENABLED /*SOC才支持EMU模块*/
#endif

#define LL_GPIO_MODULE_ENABLED
#define LL_IIC_MODULE_ENABLED
#define LL_INTC_MODULE_ENABLED
#define LL_IOCNT_MODULE_ENABLED
#define LL_KBI_MODULE_ENABLED

#if defined(RN821x_RN721x_SOC_B) || \
    defined(RN821x_RN721x_SOC_C) || \
    defined(RN821x_RN721x_SOC_D) || \
    defined(RN821x_RN721x_SOC_V2) || \
    defined(RN821x_RN721x_SOC_V3) || \
    defined(RN831x_RN861x_MCU_V2) || \
    defined(RN831x_RN861x_MCU_V3)
#define LL_LCD_MODULE_ENABLED
#endif

#if defined(RN821x_RN721x_SOC_V2) || \
    defined(RN831x_RN861x_MCU_V2)     
#define LL_M2M_MODULE_ENABLED /*MCU_V2 SOC_V2才支持M2M模块*/
#endif

#define LL_MADC_MODULE_ENABLED /*LvdCmpSar*/

#if defined(RN821x_RN721x_SOC_B) || \
    defined(RN821x_RN721x_SOC_C) || \
    defined(RN821x_RN721x_SOC_D) || \
    defined(RN821x_RN721x_SOC_V2) || \
    defined(RN821x_RN721x_SOC_V3) || \
    defined(RN202x_RN7326_SOC_V2)
#define LL_NVM_MODULE_ENABLED /*SOC才支持全失压模块*/
#endif

#define LL_RTC_MODULE_ENABLED

#if defined(RN821x_RN721x_SOC_V2) || \
    defined(RN821x_RN721x_SOC_V3) || \
    defined(RN831x_RN861x_MCU_V2) || \
    defined(RN831x_RN861x_MCU_V3) || \
    defined(RN202x_RN7326_SOC_V2)
#define LL_SIMPTC_MODULE_ENABLED
#endif

#define LL_SPI_MODULE_ENABLED
#define LL_SYSC_MODULE_ENABLED
#define LL_SYSTICK_MODULE_ENABLED
#define LL_TC_MODULE_ENABLED
#define LL_UART_MODULE_ENABLED
#define LL_WDT_MODULE_ENABLED

#if !defined(RN821x_RN721x_SOC_V3)
#define LL_ISO7816_MODULE_ENABLED /*MCU_V3 不支持ISO7816模块*/
#endif

#if defined(RN831x_RN861x_MCU_V3)
#define RN831x_RN861x_MCU_V3_LIB_API
#endif



/* Includes ------------------------------------------------------------------*/

#include "rn8xxx_ll_common.h"



#ifdef LL_D2F_MODULE_ENABLED
#include "rn8xxx_ll_d2f.h"
#endif

#ifdef LL_DSP_MODULE_ENABLED
#include "rn8xxx_ll_dsp.h"
#endif

#ifdef LL_EMU_MODULE_ENABLED
#include "rn8xxx_ll_emu.h"
#endif

#ifdef LL_GPIO_MODULE_ENABLED
#include "rn8xxx_ll_gpio.h"
#endif

#ifdef LL_IIC_MODULE_ENABLED
#include "rn8xxx_ll_iic.h"
#endif

#ifdef LL_INTC_MODULE_ENABLED
#include "rn8xxx_ll_intc.h"
#endif

#ifdef LL_IOCNT_MODULE_ENABLED
#include "rn8xxx_ll_iocnt.h"
#endif

#ifdef LL_ISO7816_MODULE_ENABLED
#include "rn8xxx_ll_iso7816.h"
#endif

#ifdef LL_KBI_MODULE_ENABLED
#include "rn8xxx_ll_kbi.h"
#endif

#ifdef LL_LCD_MODULE_ENABLED
#include "rn8xxx_ll_lcd.h"
#endif

#ifdef LL_M2M_MODULE_ENABLED
#include "rn8xxx_ll_m2m.h"
#endif


#ifdef LL_MADC_MODULE_ENABLED
#include "rn8xxx_ll_madc.h"
#endif

#ifdef LL_RTC_MODULE_ENABLED
#include "rn8xxx_ll_rtc.h"
#endif

#ifdef LL_SIMPTC_MODULE_ENABLED
#include "rn8xxx_ll_simptc.h"
#endif

#ifdef LL_SPI_MODULE_ENABLED
#include "rn8xxx_ll_spi.h"
#endif

#ifdef LL_SYSC_MODULE_ENABLED
#include "rn8xxx_ll_sysc.h"
#endif

#ifdef LL_SYSTICK_MODULE_ENABLED
#include "rn8xxx_ll_systickcortexm0.h"
#endif

#ifdef LL_TC_MODULE_ENABLED
#include "rn8xxx_ll_tc.h"
#endif

#ifdef LL_UART_MODULE_ENABLED
#include "rn8xxx_ll_uart.h"
#endif

#ifdef LL_WDT_MODULE_ENABLED
#include "rn8xxx_ll_wdt.h"
#endif


#endif /*_RN8xxx_Config_H*/
