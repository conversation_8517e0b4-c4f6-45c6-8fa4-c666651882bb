/**
 *******************************************************************************
 * @file  template/source/main.c
 * @brief Main program template for the Device Driver Library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-09-08       XT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/


/**
 * @addtogroup LL_Templates
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 * @brief  Main function of template project
 * @param  None
 * @retval int32_t return value, if needed
 */
#include "main.h"
#ifdef LL_WDT_DEMO
#include "rn8xxx_ll_wdt_demo.h"
uint8_t gu8WDT_Flag = 0;
void LL_WDT_Exe_Demo(void)
{
   
   sWDT_Cfg_TypeDef sWDT_Cfg;
   sWDT_Cfg.wdcs = WDT_WDCS_8s;
   sWDT_Cfg.Window = WDT_Window_100;
   sWDT_Cfg.Int = WDT_Int_Disable;
   sWDT_Cfg.Halt = WDT_HALT_Disable;
   sWDT_Cfg.Stby = WDT_STBY_Enable;
 
    /*初始化完成后，分别用等待发送接收，中断发送接收；DMA发送接收；来测试 */
    while(1) 
    {
        WDT->EN = 0XBB;
        if(gu8WDT_Flag)
        {
            LL_WDT_Cfg(&sWDT_Cfg); 
        }          
    }
}
#endif

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
