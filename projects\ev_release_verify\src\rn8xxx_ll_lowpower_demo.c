/**
 *******************************************************************************
 * @file  rn8xxx_ll_lowpower_demo.c
 * @brief 低功耗处理参考程序
 * 芯片低功耗的要求：
1、GPIO不连通电路
2、内部模块关闭
3、芯片工作在低频时钟32.768kHz
4、芯片休眠即运行__WFI指令

芯片复位后GPIO、模块时钟、模拟电源默认状态如下
1、GPIO在输入、输入不使能
2、模块时钟均关闭
3、模拟电源关闭 

由上面芯片复位后状态即芯片低功耗需求可见，芯片复位后，只需执行以下操作即可让芯片进入低功耗：
1、切换芯片工作时钟到32.768kHz
2、执行芯片休眠__WFI指令

实际代码可参见函数LL_Low_Power_Exe_Demo
 @verbatim
   Change Logs:  
   2024.11.4
   Single SOC V2  VBAT POWER=6.4uA
   Single SOC V3  VBAT POWER=5.5uA /VCC POWER=11.4uA
          MCU V2  VBAT POWER=6.6uA
          MCU V3  VBAT POWER=6.7uA
   three-phase SOC V2  POWER=6.8uA

 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Co., Ltd. All rights reserved.
 *

 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "main.h"
#ifdef LL_LOW_POWER_DEMO
#include "rn8xxx_ll_lowpower_demo.h"

#define SWD_EN
#define CFG_EN
/*********************************************************************************************************
** Function name:     gpioPwrDown
** Descriptions:    低功耗用GPIO配置，该函数内按照不同芯片系列，配置GPIO，实际配置值除以下两点外，其余为芯片复位值
1、合封EEPROM用SDA管脚关闭其上拉，1表示关闭上拉，防止外部接地存在漏电；MCU V2/SOC V3，合封SDA脚在P141上；MCU V3/SOC V2，合封SDA脚在P140上；三相SOC V2，合封SDA脚在P74上；
2、SWD关闭时，同步关闭其上拉
** input parameters:
** output parameters: null
** Returned value: 		null
** Note:
*********************************************************************************************************/
void gpioPwrDown(void)
{
  SYSCTL->SYS_PS = 0x82;
  SYSCTL->MOD1_EN |= (1 << 5);

#if defined(RN821x_RN721x_SOC_V2)
#ifdef SWD_EN
  GPIO->PCA0 = (1 << 29); /* keep SWD */
  GPIO->PUA = 0x00300000;
  GPIO->PIEA = 0xfeffffff; /* enable p30 input */
#else
  GPIO->PCA0 = 0; /* disable SWD */
  GPIO->PUA = 0;
  GPIO->PIEA = 0xffffffff; /* PA input disable */
#endif
  GPIO->PCA1 = 0x0;        /* GPIO mode */
  GPIO->PCA2 = 0x0;        /* GPIO mode */
  GPIO->PCA3 = 0x0;        /* GPIO mode */
  GPIO->PIEA = 0xffffffff; /* PA input disable */
  GPIO->PMA = 0xffffff1f;  /* PA input mode */
  GPIO->PIMA = 0;
  GPIO->PIMA2 = 0;

  GPIO->PIMA = 0;
  GPIO->PCB = 0x0; /*  */
  GPIO->PCB2 = 0;
  GPIO->PCB3 = 0;
  GPIO->PUB = 0;           /*  */
  GPIO->PMB = 0xffffffff;  /*  */
  GPIO->PIEB = 0xffffffff; /*  */
  GPIO->PIMB = 0;

  GPIO->PCC = 0x0;        /*  */
  GPIO->PCC2 = 0x0;       /*  */
  GPIO->PCC3 = 0x0;       /*  */
  GPIO->PCC4 = 0x0;       /*  */
  GPIO->PUC = 0x0;        /*  */
  GPIO->PMC = 0xffffffff; /* */
  GPIO->PIEC = 0xffffffff;
  GPIO->PIMC = 0;

  GPIO->PCD = 0x3fff;
  GPIO->PCD2 = 0x0;
  GPIO->PUD = (1 << 16); /*PU140=0表示开启内部上拉，芯片默认开启了上拉，此处关闭该上拉  */ 
  GPIO->PIED = 0xffffffff;
  GPIO->PIMD = 0x0;

  GPIO->PCE = 0x0;
#elif defined(RN821x_RN721x_SOC_V3)
#ifdef SWD_EN
  GPIO->PCA0 = (1 << 29); /* keep SWD */
  GPIO->PUA = 0x00300000;
  GPIO->PIEA = 0xfeffffff; /* enable p30 input */
#else
  GPIO->PCA0 = 0; /* disable SWD */
  GPIO->PUA = 0;
  GPIO->PIEA = 0xffffffff; /* PA input disable */
#endif
  GPIO->PCA1 = 0x0;        /* GPIO mode */
  GPIO->PCA2 = 0x0;        /* GPIO mode */
  GPIO->PCA3 = 0x0;        /* GPIO mode */ 
  GPIO->PMA = 0xffffff1f;  /* PA input mode */
  GPIO->PIMA = 0;
  GPIO->PIMA2 = 0;

  GPIO->PCB = 0x0; /* p60~67,p70~71,p74~77 as LCD */
  GPIO->PCB2 = 0;
  GPIO->PCB3 = 0;
  GPIO->PCB5 = 0;
  GPIO->PUB = 0;           /*  */
  GPIO->PMB = 0xffffffff;  /*  */
  GPIO->PIEB = 0xffffffff; /*  */
  GPIO->PIMB = 0;

  GPIO->PCC = 0x0;        /*  */
  GPIO->PCC2 = 0x0;       /*  */
  GPIO->PCC3 = 0x0;       /*  */
  GPIO->PCC4 = 0x0;       /*  */
  GPIO->PCC5 = 0x0;       /*  */
  GPIO->PUC = 0x0;        /*  */
  GPIO->PMC = 0xffffffff; /*  */
  GPIO->PIEC = 0xffffffff;
  GPIO->PIMC = 0;

  GPIO->PCD = 0x3fff;
  GPIO->PCD2 = 0x0;
  GPIO->PUD = (1 << 17);/*PU141=0表示开启内部上拉，芯片默认开启了上拉，此处关闭该上拉  */  
  GPIO->PIED = 0xffffffff;
  GPIO->PIMD = 0x0;

  GPIO->PCE = 0x0;
#elif defined(RN831x_RN861x_MCU_V2)
#ifdef SWD_EN
  GPIO->PCA0 = (1 << 29); /* keep SWD */
  GPIO->PUA = 0x00300000;
  GPIO->PIEA = 0xfeffffff; /* enable p30 input */
#else
  GPIO->PCA0 = 0; /* disable SWD */
  GPIO->PUA = 0;
  GPIO->PIEA = 0xffffffff; /* PA input disable */
#endif
  GPIO->PCA1 = 0x0;        /* GPIO mode */
  GPIO->PIEA = 0xffffffff; /* PA input disable */
  GPIO->PMA = 0xffffff1f;  /* PA input mode */

  GPIO->PIMA = 0;
  GPIO->PCB = 0x0; /* */
  GPIO->PCB2 = 0;
  GPIO->PMB = 0xffffffff;  /*  */
  GPIO->PIEB = 0xffffffff; /*  */
  GPIO->PIMB = 0;
  GPIO->PUB = 0; /*  */

  GPIO->PCC = 0x0;        /*  */
  GPIO->PMC = 0xffffffff; /*  */
  GPIO->PIEC = 0xffffffff;
  GPIO->PIMC = 0;

  GPIO->PCD = 0x3fff;
  GPIO->PUD = (1 << 17);/*PU141=0表示开启内部上拉，芯片默认开启了上拉，此处关闭该上拉  */
  
  GPIO->PIED = 0xffffffff;
  GPIO->PIMD = 0x0;

  GPIO->PCE = 0x0;
#elif defined(RN831x_RN861x_MCU_V3)
#ifdef SWD_EN
  GPIO->PCA0 = (1 << 29); /* keep SWD */
  GPIO->PUA = 0x00300000;
  GPIO->PIEA = 0xfeffffff; /* enable p30 input */
#else
  GPIO->PCA0 = 0; /* disable SWD */
  GPIO->PUA = 0;
  GPIO->PIEA = 0xffffffff; /* PA input disable */
#endif
  GPIO->PCA1 = 0x0;        /* GPIO mode */
  GPIO->PCA2 = 0x0;        /* GPIO mode */
  GPIO->PCA3 = 0x0;        /* GPIO mode */
  GPIO->PIEA = 0xffffffff; /* PA input disable */
  GPIO->PMA = 0xffffff1f;  /* PA input mode */
  GPIO->PIMA = 0;
  GPIO->PIMA2 = 0;

  GPIO->PCB = 0x0; /*  */
  GPIO->PCB2 = 0;
  GPIO->PCB3 = 0;
  GPIO->PCB5 = 0;
  GPIO->PUB = 0;           /*  */
  GPIO->PMB = 0xffffffff;  /*  */
  GPIO->PIEB = 0xffffffff; /*  */
  GPIO->PIMB = 0;

  GPIO->PCC = 0x0;        /*  */
  GPIO->PCC2 = 0x0;       /*  */
  GPIO->PCC4 = 0x0;       /*  */
  GPIO->PCC5 = 0x0;       /*  */
  GPIO->PMC = 0xffffffff; /*  */
  GPIO->PIEC = 0xffffffff;
  GPIO->PIMC = 0;

  GPIO->PCD = 0x3fff;
  GPIO->PUD = (1 << 16);/*PU140=0表示开启内部上拉，芯片默认开启了上拉，此处关闭该上拉  */
  GPIO->PIED = 0xffffff;
  GPIO->PIMD = 0X0;

  GPIO->PCE = 0xf;

#elif defined(RN202x_RN7326_SOC_V2)

  GPIO->PSW0 = 0x82;
  GPIO->PCA0 = 0x0; /* GPIO mode */
#ifdef SWD_EN
  GPIO->PCA1 = (1 << 1); /* keep SWD */
#else
  GPIO->PCA1 = 0x0;
#endif
  GPIO->PCA2 = 0x0;
  GPIO->PUA = 0x00030000;
  GPIO->PMA = 0xffffffff;  /* PA input mode */
  GPIO->PIEA = 0xffffffff; /* PA input disable */
  GPIO->PIMA0 = 0;
  GPIO->PIMA1 = 0;

  GPIO->PCB1 = 0;
  GPIO->PCB3 = 0;
  GPIO->PUB1 = 1 << 20; /*PU74=0表示开启内部上拉，芯片默认开启了上拉，此处关闭该上拉  */
  GPIO->PMB1 = 0xffffffff;
  GPIO->PIEB1 = 0xfffff0;
  GPIO->PIMB1 = 0;

  GPIO->PCC = 0;
  GPIO->PUC = 0;
  GPIO->PMC = 0x3ff;
  GPIO->PIMC0 = 0;
  GPIO->PIMC1 = 0;
  GPIO->PIEC = 0x3ff;

  SYSCTL->MOD1_EN |= (1 << 10);
  GPIO_APB->PSW1 = 0x82;

  GPIO_APB->PCB0 = 0;
  GPIO_APB->PCB2 = 0;
  GPIO_APB->PUB0 = 0;
  GPIO_APB->PMB0 = 0xf0f;
  GPIO_APB->PIMB0 = 0;
  GPIO_APB->PIEB0 = 0xf0f;
  GPIO_APB->PB0MASK = 0x8a5e;
#endif
}

void busPwrDown(void)
{
  SYSCTL->SYS_PS = 0x82;
#if defined(RN821x_RN721x_SOC_C)
  SYSCTL->MOD0_EN = 0x4;
#else
  SYSCTL->MOD0_EN = 0x0;
#endif

  SYSCTL->MOD1_EN = (1 << 9) | (1 << 10) | (1 << 11) | (1 << 5); // wdton,sar,rtc,gpio
  SYSCTL->KBI_EN = 0x0;
  SYSCTL->INTC_EN = 0x0;

  SYSCTL->SYS_PS = 0x0;
}

void anaPwrDown(void)
{
  SYSCTL->SYS_PS = 0x82;
#if defined(RN821x_RN721x_SOC_B) || defined(RN821x_RN721x_SOC_C)
  SYSCTL->SYS_PD |= 0xff;
#elif defined(RN821x_RN721x_SOC_V2) || defined(RN821x_RN721x_SOC_V3)
  SYSCTL->SYS_PD |= 0xff;
  SYSCTL->SYS_PD &= ~(uint32_t)(1 << 17);
#elif defined(RN831x_RN861x_MCU_V1) || defined(RN831x_RN861x_MCU_V2) || defined(RN831x_RN861x_MCU_V3)
  SYSCTL->SYS_PD |= 0x3ff;
#elif defined(RN202x_RN7326_SOC_V2)
  SYSCTL->SYS_PD |= 0x2ff0338;
#endif
  SYSCTL->SYS_PS = 0x0;
}

void socSleep(void)
{
  while (1)
  {
    __WFI();
  }
}
/************************************************ 
在芯片复位后即运行本函数LL_Low_Power_Exe_Demo，可让芯片进入低功耗模式
本函数内执行操作
1、GPIO配置成复位后默认状态
2、关闭模块时钟
3、关闭模拟电源
4、时钟切换
5、执行芯片休眠__WFI指令

本函数内1、2、3步骤由宏定义CFG_EN打开，打开后的配置用于给客户做低功耗程序参考使用，
实际应用需根据需求配置，如低功耗下需开启CMP2模块进行上电检测，此时需开启对应时钟和电源
*************************************************/
void LL_Low_Power_Exe_Demo(void)
{
#if defined(RN821x_RN721x_SOC_B) || defined(RN821x_RN721x_SOC_C)
  LL_EEPROM_Standby();
#endif

#ifdef CFG_EN
  gpioPwrDown();
  busPwrDown();
  anaPwrDown();
#endif

#if defined(RN831x_RN861x_MCU_V3)
  rn_lib->LL_SYSCLK_SysModeChg(Clock_Losc, Clock_Div_1);
#else
  LL_SYSCLK_SysModeChg(Clock_Losc, Clock_Div_1);
#endif

  socSleep();
}
#endif
