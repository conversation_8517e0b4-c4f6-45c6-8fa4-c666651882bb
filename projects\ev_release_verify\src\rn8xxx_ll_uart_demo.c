/**
 *******************************************************************************
 * @file  uart_demo.c
 * @brief Main program template for the Device Driver Library.
@verbatim
  Change Logs:
  Date             Author          Notes
  2023-09-08       XT             First version
@endverbatim
一、功能说明
中断接收模式即如下定义：
#define BSP_INTERRUPT_TRANS 6或
可实现串口工具发给RN MCU的数据，从RN MCU串口发送出去的功能，即
实际测试时：用串口工具，给芯片发数据；串口工具应能收到，其所发送数据；

二、本用例默认配置：
波特率4800，偶校验，8位数据为，1位停止位；BSP_INTERRUPT_SEND模式
三相SOC：打印输出UART1，输出管脚为P82 P83；测试UART3，输出管脚为P26 P27
其他产品：打印输出UART1，输出管脚为P22 P23；测试UART2，输出管脚为P26 P27
如需修改打印输出串口，需同步修改，serial.c中宏定义

三、测试demo使用步骤：

1、在该文件内定义所要测试的串口号及相应的GPIO，如：
#define TEST_UART_ID226    //用于串口测试
#define PRINT_UART_ID122
1//用于打印输出，建议固定为1，且不要与被测串口冲突；另外，为保证正确打印，需同步修改serial.c文件内的打印串口号，默认为1；因为该文件未包含此处宏定义，需单独定义

为方便使用，本文件内已提前定义相应的串口号对应的管脚，只需打开相应定义即可，一次只能打开一组：
其中第一个表示串口号，后面的两个数字表示UART接口序号小的GPIO管脚编号，如TEST_UART_ID020，表示P20 P21管脚上的UART0
当前支持的UART口选择有：
TEST_UART_ID020 :UART0 P20 P21
TEST_UART_ID122 :UART1 P22 P23
TEST_UART_ID200 :UART2 P00 P01

注：三相SOCP20 P21，其他芯片P24 P25与SWD管脚复用，为防止连不上JLINK，仿真中断，DEMO中未定义其作为UART使用

2、选择串口发送和接收方式

支持多个串口在不同GPIO上的选择，具体使用方法只参考一种即可

BSP_TRANS_MODE
BSP_DMA_TRANS
BSP_DMA_SEND
BSP_DELAY_SEND
BSP_DELAY_RECV
BSP_DMA_RECV

3、本用例中还给出了RN8209 UART通讯的示例，可通过宏开关启动，默认情况下，不启动RN8209的UART通讯，如需使用，需要添加相应目录及文件
#define UART_DEMO_RN8209
默认不开启

为了BSP_RN8209.C/H可以单独使用

4、若只想使用UART作为打印输出，可进行如下定义：
#define LL_UART_PRING

5、若只想验证DMA参数分项配置功能，可进行如下定义：
#define BSP_UART_DMA_STRUCT

 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "main.h"
#ifdef LL_UART_DEMO
#include "rn8xxx_ll_uart_demo.h"
#include "rn8xxx_ll.h"

#define BSP_DELAY_SEND 1
#define BSP_DELAY_RECV 2
#define BSP_DMA_CYC_TRANS 3
#define BSP_DMA_SEND 4
#define BSP_DMA_RECV 5
#define BSP_INTERRUPT_TRANS 6

#define BSP_TRANS_MODE BSP_INTERRUPT_TRANS

// #define UART_DEMO_RN8209
#ifdef UART_DEMO_RN8209
#include "BSP_RN8209.h"
#endif

// #define LL_UART_PRING /* UART只做PRINT输出使用 */
// #define BSP_UART_DMA_STRUCT  /* DMA直接用结构体初始化启动；不定义则用分离函数初始化 */

#if (BSP_TRANS_MODE == BSP_DMA_SEND) || (BSP_TRANS_MODE == BSP_DMA_RECV)
#define BSP_DMA_TRANS
#endif

#if defined(RN821x_RN721x_SOC_V2) || defined(RN831x_RN861x_MCU_V2)
// #define TEST_UART_ID020
// #define TEST_UART_ID122
// #define TEST_UART_ID200
// #define TEST_UART_ID222
#define TEST_UART_ID226
// #define TEST_UART_ID326
// #define TEST_UART_ID430
// #define TEST_UART_ID446
// #define TEST_UART_ID554

// #define PRINT_UART_ID020
#define PRINT_UART_ID122
// #define PRINT_UART_ID200
// #define PRINT_UART_ID222
// #define PRINT_UART_ID226
// #define PRINT_UART_ID326
// #define PRINT_UART_ID430
// #define PRINT_UART_ID554

#elif defined(RN821x_RN721x_SOC_V3)
// #define TEST_UART_ID200
// #define TEST_UART_ID446
// #define TEST_UART_ID554
// #define TEST_UART_ID340
// #define TEST_UART_ID442
// #define TEST_UART_ID020
// #define TEST_UART_ID220
// #define TEST_UART_ID122
#define TEST_UART_ID226
// #define TEST_UART_ID326

#define PRINT_UART_ID122
// #define PRINT_UART_ID200

#elif defined(RN831x_RN861x_MCU_V3)

// #define TEST_UART_ID200 /* 00=rx  01=tx */
// #define TEST_UART_ID4104  /* 104=tx  105=rx */
// #define TEST_UART_ID5106  /* 106=ex  106=tx */
// #define TEST_UART_ID640 /* 40=rx  41=tx */
// #define TEST_UART_ID742   /* 42=rx  43=tx */
// #define TEST_UART_ID446   /* 46=rx  47=tx */
// #define TEST_UART_ID554   /* 54=rx  55=tx */
// #define TEST_UART_ID020   /* 20=rx  21=tx */
// #define TEST_UART_ID220   /* 20=rx  21=tx */
// #define TEST_UART_ID122   /* 22=rx  23=tx */
// #define TEST_UART_ID222   /* 22=rx  23=tx */
#define TEST_UART_ID226 /* 26=rx  27=tx */
// #define TEST_UART_ID326   /* 26=rx  27=tx */

#define PRINT_UART_ID122
// #define PRINT_UART_ID446

#elif defined(RN202x_RN7326_SOC_V2)
// #define TEST_UART_ID004
// #define TEST_UART_ID060
// #define TEST_UART_ID082
// #define TEST_UART_ID120/* 与swd共用，使用后，会无法连接swd；请谨慎使用 */
// #define TEST_UART_ID160
// #define TEST_UART_ID182
// #define TEST_UART_ID204
// #define TEST_UART_ID222
// #define TEST_UART_ID233
// #define TEST_UART_ID260
// #define TEST_UART_ID282
#define TEST_UART_ID326
// #define TEST_UART_ID360
// #define TEST_UART_ID382
// #define TEST_UART_ID444
// #define TEST_UART_ID460
// #define TEST_UART_ID482
// #define TEST_UART_ID546
// #define TEST_UART_ID560
// #define TEST_UART_ID582

// #define PRINT_UART_ID004
// #define PRINT_UART_ID060
// #define PRINT_UART_ID082
// #define PRINT_UART_ID160
#define PRINT_UART_ID182
// #define PRINT_UART_ID204
// #define PRINT_UART_ID220
// #define PRINT_UART_ID222
// #define PRINT_UART_ID234
// #define PRINT_UART_ID260
// #define PRINT_UART_ID282
// #define PRINT_UART_ID326
// #define PRINT_UART_ID360
// #define PRINT_UART_ID382
// #define PRINT_UART_ID444
// #define PRINT_UART_ID460
// #define PRINT_UART_ID482
// #define PRINT_UART_ID546
// #define PRINT_UART_ID560
// #define PRINT_UART_ID582
#endif

#if defined(TEST_UART_ID004)
#define TEST_UART_ID UART0_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART0_ID
#define BSP_UART_ID UART0
#define BSP_GPIO_UART _UART0
#define BSP_UART_RX PIN_0_4
#define BSP_UART_TX PIN_0_5
#elif defined(TEST_UART_ID020)
#define TEST_UART_ID UART0_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART0_ID
#define BSP_UART_ID UART0
#define BSP_GPIO_UART _UART0
#define BSP_UART_RX PIN_2_0
#define BSP_UART_TX PIN_2_1
#elif defined(TEST_UART_ID060)
#define TEST_UART_ID UART0_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART0_ID
#define BSP_UART_ID UART0
#define BSP_GPIO_UART _UART0
#define BSP_UART_RX PIN_6_0
#define BSP_UART_TX PIN_6_1
#elif defined(TEST_UART_ID082)
#define TEST_UART_ID UART0_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART0_ID
#define BSP_UART_ID UART0
#define BSP_GPIO_UART _UART0
#define BSP_UART_RX PIN_8_3
#define BSP_UART_TX PIN_8_2
#elif defined(TEST_UART_ID120)
#define TEST_UART_ID UART1_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART1_ID
#define BSP_UART_ID UART1
#define BSP_GPIO_UART _UART1
#define BSP_UART_RX PIN_2_0
#define BSP_UART_TX PIN_2_1
#elif defined(TEST_UART_ID122)
#define TEST_UART_ID UART1_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART1_ID
#define BSP_UART_ID UART1
#define BSP_GPIO_UART _UART1
#define BSP_UART_RX PIN_2_2
#define BSP_UART_TX PIN_2_3
#elif defined(TEST_UART_ID160)
#define TEST_UART_ID UART1_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART1_ID
#define BSP_UART_ID UART1
#define BSP_GPIO_UART _UART1
#define BSP_UART_RX PIN_6_0
#define BSP_UART_TX PIN_6_1
#elif defined(TEST_UART_ID182)
#define TEST_UART_ID UART1_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART1_ID
#define BSP_UART_ID UART1
#define BSP_GPIO_UART _UART1
#define BSP_UART_RX PIN_8_3
#define BSP_UART_TX PIN_8_2
#elif defined(TEST_UART_ID200)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_0_0
#define BSP_UART_TX PIN_0_1
#elif defined(TEST_UART_ID204)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_0_4
#define BSP_UART_TX PIN_0_5
#elif defined(TEST_UART_ID220)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_2_0
#define BSP_UART_TX PIN_2_1
#elif defined(TEST_UART_ID222)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_2_2
#define BSP_UART_TX PIN_2_3
#elif defined(TEST_UART_ID226)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_2_6
#define BSP_UART_TX PIN_2_7
#elif defined(TEST_UART_ID233)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_3_3
#define BSP_UART_TX PIN_3_4
#elif defined(TEST_UART_ID260)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_6_0
#define BSP_UART_TX PIN_6_1
#elif defined(TEST_UART_ID282)
#define TEST_UART_ID UART2_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART2_ID
#define BSP_UART_ID UART2
#define BSP_GPIO_UART _UART2
#define BSP_UART_RX PIN_8_3
#define BSP_UART_TX PIN_8_2
#elif defined(TEST_UART_ID326)
#define TEST_UART_ID UART3_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART3_ID
#define BSP_UART_ID UART3
#define BSP_GPIO_UART _UART3
#define BSP_UART_RX PIN_2_6
#define BSP_UART_TX PIN_2_7
#elif defined(TEST_UART_ID340)
#define TEST_UART_ID UART3_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART3_ID
#define BSP_UART_ID UART3
#define BSP_GPIO_UART _UART3
#define BSP_UART_RX PIN_4_1
#define BSP_UART_TX PIN_4_0
#elif defined(TEST_UART_ID360)
#define TEST_UART_ID UART3_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART3_ID
#define BSP_UART_ID UART3
#define BSP_GPIO_UART _UART3
#define BSP_UART_RX PIN_6_0
#define BSP_UART_TX PIN_6_1
#elif defined(TEST_UART_ID382)
#define TEST_UART_ID UART3_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART3_ID
#define BSP_UART_ID UART3
#define BSP_GPIO_UART _UART3
#define BSP_UART_RX PIN_8_3
#define BSP_UART_TX PIN_8_2
#elif defined(TEST_UART_ID431)
#define TEST_UART_ID UART4_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART4_ID
#define BSP_UART_ID UART4
#define BSP_GPIO_UART _UART4
#define BSP_UART_RX PIN_3_1
#define BSP_UART_TX PIN_3_0
#elif defined(TEST_UART_ID442)
#define TEST_UART_ID UART4_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART4_ID
#define BSP_UART_ID UART4
#define BSP_GPIO_UART _UART4
#define BSP_UART_RX PIN_4_3
#define BSP_UART_TX PIN_4_2
#elif defined(TEST_UART_ID444)
#define TEST_UART_ID UART4_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART4_ID
#define BSP_UART_ID UART4
#define BSP_GPIO_UART _UART4
#define BSP_UART_RX PIN_4_4
#define BSP_UART_TX PIN_4_5
#elif defined(TEST_UART_ID446)
#define TEST_UART_ID UART4_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART4_ID
#define BSP_UART_ID UART4
#define BSP_GPIO_UART _UART4
#define BSP_UART_RX PIN_4_6
#define BSP_UART_TX PIN_4_7
#elif defined(TEST_UART_ID460)
#define TEST_UART_ID UART4_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART4_ID
#define BSP_UART_ID UART4
#define BSP_GPIO_UART _UART4
#define BSP_UART_RX PIN_6_0
#define BSP_UART_TX PIN_6_1
#elif defined(TEST_UART_ID482)
#define TEST_UART_ID UART4_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART4_ID
#define BSP_UART_ID UART4
#define BSP_GPIO_UART _UART4
#define BSP_UART_RX PIN_8_3
#define BSP_UART_TX PIN_8_2
#elif defined(TEST_UART_ID4104)
#define TEST_UART_ID UART4_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART4_ID
#define BSP_UART_ID UART4
#define BSP_GPIO_UART _UART4
#define BSP_UART_RX PIN_10_5
#define BSP_UART_TX PIN_10_4
#elif defined(TEST_UART_ID546)
#define TEST_UART_ID UART5_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART5_ID
#define BSP_UART_ID UART5
#define BSP_GPIO_UART _UART5
#define BSP_UART_RX PIN_4_7
#define BSP_UART_TX PIN_4_6
#elif defined(TEST_UART_ID554)
#define TEST_UART_ID UART5_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART5_ID
#define BSP_UART_ID UART5
#define BSP_GPIO_UART _UART5
#define BSP_UART_RX PIN_5_4
#define BSP_UART_TX PIN_5_5
#elif defined(TEST_UART_ID560)
#define TEST_UART_ID UART5_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART5_ID
#define BSP_UART_ID UART5
#define BSP_GPIO_UART _UART5
#define BSP_UART_RX PIN_6_0
#define BSP_UART_TX PIN_6_1
#elif defined(TEST_UART_ID582)
#define TEST_UART_ID UART5_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART5_ID
#define BSP_UART_ID UART5
#define BSP_GPIO_UART _UART5
#define BSP_UART_RX PIN_8_3
#define BSP_UART_TX PIN_8_2
#elif defined(TEST_UART_ID5106)
#define TEST_UART_ID UART5_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART5_ID
#define BSP_UART_ID UART5
#define BSP_GPIO_UART _UART5
#define BSP_UART_RX PIN_10_6
#define BSP_UART_TX PIN_10_7
#elif defined(TEST_UART_ID640)
#define TEST_UART_ID UART6_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART6_ID
#define BSP_UART_ID UART6
#define BSP_GPIO_UART _UART6
#define BSP_UART_RX PIN_4_0
#define BSP_UART_TX PIN_4_1
#elif defined(TEST_UART_ID742)
#define TEST_UART_ID UART7_ID
#define BSP_SYSC_UART_ID LL_SYSC_UART7_ID
#define BSP_UART_ID UART7
#define BSP_GPIO_UART _UART7
#define BSP_UART_RX PIN_4_2
#define BSP_UART_TX PIN_4_3
#endif

#if defined(PRINT_UART_ID004)
#define PRINT_UART_ID UART0_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART0_ID
#define PRINT_UART_ID UART0
#define PRINT_GPIO_UART _UART0
#define PRINT_UART_RX PIN_0_4
#define PRINT_UART_TX PIN_0_5
#elif defined(PRINT_UART_ID020)
#define PRINT_UART_ID UART0_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART0_ID
#define PRINT_UART_ID UART0
#define PRINT_GPIO_UART _UART0
#define PRINT_UART_RX PIN_2_0
#define PRINT_UART_TX PIN_2_1
#elif defined(PRINT_UART_ID060)
#define PRINT_UART_ID UART0_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART0_ID
#define PRINT_UART_ID UART0
#define PRINT_GPIO_UART _UART0
#define PRINT_UART_RX PIN_6_0
#define PRINT_UART_TX PIN_6_1
#elif defined(PRINT_UART_ID082)
#define PRINT_UART_ID UART0_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART0_ID
#define PRINT_UART_ID UART0
#define PRINT_GPIO_UART _UART0
#define PRINT_UART_RX PIN_8_2
#define PRINT_UART_TX PIN_8_3
#elif defined(PRINT_UART_ID120)
#define PRINT_UART_ID UART1_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART1_ID
#define PRINT_UART_ID UART1
#define PRINT_GPIO_UART _UART1
#define PRINT_UART_RX PIN_2_0
#define PRINT_UART_TX PIN_2_1
#elif defined(PRINT_UART_ID122)
#define PRINT_UART_ID UART1_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART1_ID
#define PRINT_UART_ID UART1
#define PRINT_GPIO_UART _UART1
#define PRINT_UART_RX PIN_2_2
#define PRINT_UART_TX PIN_2_3
#elif defined(PRINT_UART_ID160)
#define PRINT_UART_ID UART1_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART1_ID
#define PRINT_UART_ID UART1
#define PRINT_GPIO_UART _UART1
#define PRINT_UART_RX PIN_6_0
#define PRINT_UART_TX PIN_6_1
#elif defined(PRINT_UART_ID182)
#define PRINT_UART_ID UART1_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART1_ID
#define PRINT_UART_ID UART1
#define PRINT_GPIO_UART _UART1
#define PRINT_UART_TX PIN_8_2
#define PRINT_UART_RX PIN_8_3
#elif defined(PRINT_UART_ID200)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_0_0
#define PRINT_UART_TX PIN_0_1
#elif defined(PRINT_UART_ID204)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_0_4
#define PRINT_UART_TX PIN_0_5
#elif defined(PRINT_UART_ID220)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_2_0
#define PRINT_UART_TX PIN_2_1
#elif defined(PRINT_UART_ID222)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_2_2
#define PRINT_UART_TX PIN_2_3
#elif defined(PRINT_UART_ID226)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_2_6
#define PRINT_UART_TX PIN_2_7
#elif defined(PRINT_UART_ID234)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_3_4
#define PRINT_UART_TX PIN_3_5
#elif defined(PRINT_UART_ID260)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_6_0
#define PRINT_UART_TX PIN_6_1
#elif defined(PRINT_UART_ID282)
#define PRINT_UART_ID UART2_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART2_ID
#define PRINT_UART_ID UART2
#define PRINT_GPIO_UART _UART2
#define PRINT_UART_RX PIN_8_2
#define PRINT_UART_TX PIN_8_3
#elif defined(PRINT_UART_ID326)
#define PRINT_UART_ID UART3_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART3_ID
#define PRINT_UART_ID UART3
#define PRINT_GPIO_UART _UART3
#define PRINT_UART_RX PIN_2_6
#define PRINT_UART_TX PIN_2_7
#elif defined(PRINT_UART_ID360)
#define PRINT_UART_ID UART3_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART3_ID
#define PRINT_UART_ID UART3
#define PRINT_GPIO_UART _UART3
#define PRINT_UART_RX PIN_6_0
#define PRINT_UART_TX PIN_6_1
#elif defined(PRINT_UART_ID382)
#define PRINT_UART_ID UART3_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART3_ID
#define PRINT_UART_ID UART3
#define PRINT_GPIO_UART _UART3
#define PRINT_UART_RX PIN_8_2
#define PRINT_UART_TX PIN_8_3
#elif defined(PRINT_UART_ID431)
#define PRINT_UART_ID UART4_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART4_ID
#define PRINT_UART_ID UART4
#define PRINT_GPIO_UART _UART4
#define PRINT_UART_RX PIN_3_1
#define PRINT_UART_TX PIN_3_0
#elif defined(PRINT_UART_ID444)
#define PRINT_UART_ID UART4_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART4_ID
#define PRINT_UART_ID UART4
#define PRINT_GPIO_UART _UART4
#define PRINT_UART_RX PIN_4_4
#define PRINT_UART_TX PIN_4_5
#elif defined(PRINT_UART_ID446)
#define PRINT_UART_ID UART4_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART4_ID
#define PRINT_UART_ID UART4
#define PRINT_GPIO_UART _UART4
#define PRINT_UART_RX PIN_4_6
#define PRINT_UART_TX PIN_4_7
#elif defined(PRINT_UART_ID460)
#define PRINT_UART_ID UART4_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART4_ID
#define PRINT_UART_ID UART4
#define PRINT_GPIO_UART _UART4
#define PRINT_UART_RX PIN_6_0
#define PRINT_UART_TX PIN_6_1
#elif defined(PRINT_UART_ID482)
#define PRINT_UART_ID UART4_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART4_ID
#define PRINT_UART_ID UART4
#define PRINT_GPIO_UART _UART4
#define PRINT_UART_RX PIN_8_2
#define PRINT_UART_TX PIN_8_3
#elif defined(PRINT_UART_ID546)
#define PRINT_UART_ID UART5_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART5_ID
#define PRINT_UART_ID UART5
#define PRINT_GPIO_UART _UART5
#define PRINT_UART_RX PIN_4_6
#define PRINT_UART_TX PIN_4_7
#elif defined(PRINT_UART_ID554)
#define PRINT_UART_ID UART5_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART5_ID
#define PRINT_UART_ID UART5
#define PRINT_GPIO_UART _UART5
#define PRINT_UART_RX PIN_5_4
#define PRINT_UART_TX PIN_5_5
#elif defined(PRINT_UART_ID560)
#define PRINT_UART_ID UART5_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART5_ID
#define PRINT_UART_ID UART5
#define PRINT_GPIO_UART _UART5
#define PRINT_UART_RX PIN_6_0
#define PRINT_UART_TX PIN_6_1
#elif defined(PRINT_UART_ID582)
#define PRINT_UART_ID UART5_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART5_ID
#define PRINT_UART_ID UART5
#define PRINT_GPIO_UART _UART5
#define PRINT_UART_RX PIN_8_2
#define PRINT_UART_TX PIN_8_3
#elif defined(PRINT_UART_ID640)
#define PRINT_UART_ID UART6_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART6_ID
#define PRINT_UART_ID UART6
#define PRINT_GPIO_UART _UART6
#define PRINT_UART_RX PIN_4_0
#define PRINT_UART_TX PIN_4_1
#elif defined(PRINT_UART_ID742)
#define PRINT_UART_ID UART7_ID
#define PRINT_SYSC_UART_ID LL_SYSC_UART7_ID
#define PRINT_UART_ID UART7
#define PRINT_GPIO_UART _UART7
#define PRINT_UART_RX PIN_4_2
#define PRINT_UART_TX PIN_4_3
#endif

#define MAX_TRANS_RECV_LEN 10

uint8_t gu8Uart_Trans_Flag = 0;
uint8_t gu8UartRecvBuf[MAX_TRANS_RECV_LEN];
uint8_t gu8UartSendBuf[MAX_TRANS_RECV_LEN] = {1, 2, 3, 4, 5, 6, 7, 8, 9, 10};
typedef struct
{
    uint8_t *pu8Send;
    uint8_t *pu8Recv;
    uint16_t u16SendLen;
    uint16_t u16RecvLen;
} sTrans_TypeDef;
sTrans_TypeDef          gsTrans;
sLL_UART_InitTypeDef    sUART_Init;
sLL_UARTDMA_InitTypeDef sUARTDMA_StructInit;

uint8_t gStart_Cmd;

typedef union
{
    uint8_t  u8Data[4];
    uint16_t u16Data[2];
    uint32_t u32Data;
} uRN8209_ComBuf_TypeDef;
uRN8209_ComBuf_TypeDef ugRN8209_ComBuf[4];
/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

void BSP_Delay(uint32_t time)
{
    uint32_t i = 0, j = 0;
    for(i = 0; i < time; i++)
    {
        for(j = 0; j < 100; j++) { ; }
        WDT->EN = 0XBB;
    }
}
void BSP_LL_UART_StructInit(sLL_UART_InitTypeDef *sUART_Init)
{
    /* Set USART_InitStruct fields to default values */
    sUART_Init->BaudRate = 4800;

    sUART_Init->Ctrl.bitUartCtrl.EN     = UARTCTL_EN;
    sUART_Init->Ctrl.bitUartCtrl.TXIE   = UARTCTL_TXIE_DIS;
    sUART_Init->Ctrl.bitUartCtrl.RXIE   = UARTCTL_RXIE_EN;
    sUART_Init->Ctrl.bitUartCtrl.ERRIE  = UARTCTL_ERRIE_EN;
    sUART_Init->Ctrl.bitUartCtrl.STOPS  = UARTCTL_1STOP_SBIT;
    sUART_Init->Ctrl.bitUartCtrl.DATLEN = UARTCTL_8BIT_DAT;
    sUART_Init->Ctrl.bitUartCtrl.PARS   = UARTCTL_EVEN_PARITY;
    sUART_Init->Ctrl.bitUartCtrl.IRE    = UARTCTL_IRE_DIS;
    sUART_Init->Ctrl.bitUartCtrl.ILBE   = UARTCTL_ILBE_DIS; /*内部回环使能，若使能该选项，芯片内部UART接收管脚短接到发送管脚，外部输入信号会被屏蔽，正常应用不要使能该位*/
    sUART_Init->Ctrl.bitUartCtrl.IRSEL  = UARTCTL_IRSEL_H;
    sUART_Init->Ctrl.bitUartCtrl.LMSB   = UARTCTL_LSB;
    sUART_Init->Ctrl.bitUartCtrl.NEG    = UARTCTL_NEG_H;
}
void BSP_LL_UARTDMA_StructInit(sLL_UARTDMA_InitTypeDef *sUARTDMA_StructInit)
{
    uint8_t *pu8SendBuf = gu8UartSendBuf;
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    /* Set USART_InitStruct fields to default values */
    sUARTDMA_StructInit->CTL.bitUartDmaCtrl.TX_DMA_EN   = UARTDMA_TX_EN;      /*"发送DMA使能"*/
    sUARTDMA_StructInit->CTL.bitUartDmaCtrl.RX_DMA_EN   = UARTDMA_RX_EN;      /*"接收DMA使能"*/
    sUARTDMA_StructInit->CTL.bitUartDmaCtrl.TX_CYC_MODE = UARTDMA_TX_CYC_DIS; /*"发送循环模式关闭"*/
    sUARTDMA_StructInit->CTL.bitUartDmaCtrl.RX_CYC_MODE = UARTDMA_RX_CYC_DIS; /*"接收循环模式关闭"*/

    sUARTDMA_StructInit->TBADR = (uint32_t)pu8SendBuf & 0xffff;
    sUARTDMA_StructInit->RBADR = (uint32_t)pu8RecvBuf & 0xffff;
    sUARTDMA_StructInit->TLEN  = 10;
    sUARTDMA_StructInit->RLEN  = 20;
    sUARTDMA_StructInit->IE    = 0;
}

void BSP_GPIO_PxxForUARTxRxInit(uint32_t pxx, eGPIOFunction_TypeDef uartx)
{
    GPIO_InitTypeDef GPIO_Init;
    GPIO_Init.Pin = pxx;                  /*!< Specifies the GPIO pins to be configured.
                                   This parameter can be any value of @ref GPIO_pins */
    GPIO_Init.Mode        = uartx;        /*!< 复用模式 */
    GPIO_Init.OutputLevel = Low_Level;    /*"输出电平选择"*/
    GPIO_Init.Pull        = Pull_OFF;     /*!< 上拉选择 */
    GPIO_Init.Dir         = GPIO_MODE_IN; /*!< 输入输出选择 */
    GPIO_Init.InputMode   = TTL_MODE;     /*!< 输入模式*/
    GPIO_Init.OutputMode  = PushPll_MODE; /*!< 输出模式*/

    LL_GPIO_Init(&GPIO_Init);
}
void BSP_GPIO_PxxForUARTxTxInit(uint32_t pxx, eGPIOFunction_TypeDef uartx)
{
    GPIO_InitTypeDef GPIO_Init;
    GPIO_Init.Pin = pxx;                   /*!< Specifies the GPIO pins to be configured.
                                    This parameter can be any value of @ref GPIO_pins */
    GPIO_Init.Mode        = uartx;         /*!< 复用模式 */
    GPIO_Init.OutputLevel = Low_Level;     /*"输出电平选择"*/
    GPIO_Init.Pull        = Pull_OFF;      /*!< 上拉选择 */
    GPIO_Init.Dir         = GPIO_MODE_OUT; /*!< 输入输出选择 */
    GPIO_Init.InputMode   = TTL_MODE;      /*!< 输入模式*/
    GPIO_Init.OutputMode  = PushPll_MODE;  /*!< 输出模式*/

    LL_GPIO_Init(&GPIO_Init);
}

void BSP_UART_DelaySend(void)
{
    uint8_t  u8i        = 0;
    uint8_t *pu8SendBuf = gu8UartSendBuf;
    // for (u8i = 0; u8i < 10; u8i++)
    // {
    //   LL_UART_Write(BSP_UART_ID, *pu8SendBuf++);
    // }
    LL_UART_Write(BSP_UART_ID, 0X7F);

    BSP_Delay(10000);
}

void BSP_UART_DelayRecv(sTrans_TypeDef *sTrans)
{
    *sTrans->pu8Recv = LL_UART_Read(BSP_UART_ID);
    if(sTrans->u16RecvLen < MAX_TRANS_RECV_LEN)
    {
        sTrans->pu8Recv++;
        sTrans->u16RecvLen++;
    }
    else
    {
        sTrans->pu8Recv -= MAX_TRANS_RECV_LEN;
        sTrans->u16RecvLen = 0;
    }
}
volatile uint8_t u8RecvLen = 0;
volatile uint8_t u8SendLen = 0;

void BSP_MAIN_gsTrans_Init(void)
{
    gsTrans.pu8Recv    = gu8UartRecvBuf;
    gsTrans.pu8Send    = gu8UartSendBuf;
    gsTrans.u16SendLen = 0;
    gsTrans.u16RecvLen = 0;
}

#if defined(RN821x_RN721x_SOC_V2) || defined(RN831x_RN861x_MCU_V2) || defined(RN821x_RN721x_SOC_V3) || defined(RN831x_RN861x_MCU_V3) || defined(RN202x_RN7326_SOC_B) || \
    defined(RN202x_RN7326_SOC_V2)
void BSP_UART_DmaSend(void)
{
    BSP_LL_UARTDMA_StructInit(&sUARTDMA_StructInit);
    LL_UART_DMAInit(BSP_UART_ID, sUARTDMA_StructInit);

    u8SendLen = LL_UART_DMATxInfo(BSP_UART_ID);

    BSP_Delay(1000);
}

uint16_t BSP_UART_DmaRecv(uint8_t *start_cmd)
{
    uint16_t u16RecvLen = 0;
    if(*start_cmd == 1)
    {
        *start_cmd = 0;
        BSP_LL_UARTDMA_StructInit(&sUARTDMA_StructInit);
        LL_UART_DMAInit(BSP_UART_ID, sUARTDMA_StructInit);
    }

    u16RecvLen = LL_UART_DMARxInfo(BSP_UART_ID);

    return (u8RecvLen);
}
#endif
void BSP_UART_InterruptSend(void)
{
    uint8_t *pu8SendBuf = gu8UartSendBuf;
    LL_UART_Write(BSP_UART_ID, *pu8SendBuf++);
}
void BSP_UART_InterruptRecv(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf++       = LL_UART_Read(BSP_UART_ID);
}
/*
本用例在中断内接收数据，并将接收到的数据转发出去，用于UART的收发测试；
实际测试时：用串口工具，给芯片发数据；串口工具应能收到，其所发送数据；
本用例中只开启了UART的接收和错误中断，没有开启UART的发送中断
如若开启UART发送中断，注意增加清发送中断的代码，防止有产生中断的标志没有被清除，导致反复进中断
 */
uint32_t BSP_UART_InterruptTrans(UART_TypeDef *UARTx)
{
    uint32_t u32_recdata = 0, u32_status = 0;
    u32_status = READ_REG(UARTx->STA);

    if(UART_STA_RX == (u32_status & UART_STA_RX))
    { /* UART接收完成中断，读后会清零，故需先保存状态，后使用 */
        u32_recdata = LL_UART_Read(BSP_UART_ID);

        if(0 == GET_BIT_UART_STA(UARTx, UART_STA_TB))
        { /* 此处测试用，将uart收到的数据，转发出去；实际应用根据需要进行发送 */
            LL_UART_Write(BSP_UART_ID, u32_recdata);
            SET_BIT_TO_CLR_UART_STA(UARTx, UART_STA_TX);
        }

        SET_BIT_TO_CLR_UART_STA(UARTx, UART_STA_RX);
    }
    SET_REG_UART_STA(UARTx, u32_status);
    return (u32_recdata);
}

void UART0_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}

void UART1_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}

void UART2_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}
void UART3_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}

void UART4_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}
void UART5_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}

#if defined(RN831x_RN861x_MCU_V3)
void ISO78160_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}
void ISO78161_HANDLER(void)
{
    uint8_t *pu8RecvBuf = gu8UartRecvBuf;
    *pu8RecvBuf         = BSP_UART_InterruptTrans(BSP_UART_ID);
}
#endif

/*本代码，主要用于UART模块驱动中各函数的应用示例，
通过调整宏定义BSP_TRANS_MODE，选择使用UART的三种数据传输方式：等待传输、中断传输、DMA传输
通过调整宏定义BSP_UART_ID选择不同串口，注意选择不同串口后，要同步选择系统时钟宏定义BSP_SYSC_UART_ID及相应GPIO宏定义BSP_UART_RX和BSP_UART_TX
 */

void LL_UART_Exe_Demo(void)
{
    uint8_t  u8i        = 0, *start_cmd;
    uint32_t u32emu_buf = 0;
    start_cmd           = &gStart_Cmd;
    *start_cmd          = 1;
    BSP_MAIN_gsTrans_Init(); /* 初始化用串口测试用到的全局变量 */

    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);
    BSP_GPIO_PxxForUARTxRxInit(PRINT_UART_RX, PRINT_GPIO_UART);
    BSP_GPIO_PxxForUARTxTxInit(PRINT_UART_TX, PRINT_GPIO_UART);
    LL_SYSC_ApbClkCtrl(PRINT_SYSC_UART_ID, ERN_ENABLE);
    BSP_LL_UART_StructInit(&sUART_Init);
    LL_UART_Init(PRINT_UART_ID, &sUART_Init);

    /* 只做print输出使用，不启动以下UART测试代码 */
    BSP_GPIO_PxxForUARTxRxInit(BSP_UART_RX, BSP_GPIO_UART);
    BSP_GPIO_PxxForUARTxTxInit(BSP_UART_TX, BSP_GPIO_UART);
    LL_SYSC_ApbClkCtrl(BSP_SYSC_UART_ID, ERN_ENABLE);
    BSP_LL_UART_StructInit(&sUART_Init);
    LL_UART_Init(BSP_UART_ID, &sUART_Init);
#if (BSP_TRANS_MODE == BSP_INTERRUPT_TRANS)
    if(TEST_UART_ID == UART4_ID) { /*中断号UART4、UART5与UART3不连续*/ NVIC_EnableIRQ(UART4_IRQn); }
    else if(TEST_UART_ID == UART5_ID) { NVIC_EnableIRQ(UART5_IRQn); }
#if defined(RN831x_RN861x_MCU_V3)
    else if(TEST_UART_ID == UART6_ID) { NVIC_EnableIRQ(ISO78160_IRQn); }
    else if(TEST_UART_ID == UART7_ID) { NVIC_EnableIRQ(ISO78161_IRQn); }
#endif
    else { NVIC_EnableIRQ((UART0_IRQn + TEST_UART_ID)); }
#endif
#ifdef BSP_DMA_TRANS
    BSP_LL_UARTDMA_StructInit(&sUARTDMA_StructInit);
#ifdef BSP_UART_DMA_STRUCT
    LL_UART_DMAInit(BSP_UART_ID, sUARTDMA_StructInit);
#else
    LL_UART_DMA_SetRecAddr(BSP_UART_ID, sUARTDMA_StructInit.RBADR);
    LL_UART_DMA_SetRecLen(BSP_UART_ID, sUARTDMA_StructInit.RLEN);
    LL_UART_DMA_SetTransAddr(BSP_UART_ID, sUARTDMA_StructInit.TBADR);
    LL_UART_DMA_SetTransLen(BSP_UART_ID, sUARTDMA_StructInit.TLEN);

    LL_UART_DMA_IEConfig(BSP_UART_ID, UARTDMA_TX_HIE, ERN_ENABLE);
    LL_UART_DMA_IEConfig(BSP_UART_ID, UARTDMA_TX_FIE, ERN_ENABLE);
    LL_UART_DMA_IEConfig(BSP_UART_ID, UARTDMA_RX_HIE, ERN_ENABLE);
    LL_UART_DMA_IEConfig(BSP_UART_ID, UARTDMA_RX_FIE, ERN_ENABLE);
    LL_UART_DMA_IEConfig(BSP_UART_ID, UARTDMA_TX_ERR_IE, ERN_ENABLE);
    LL_UART_DMA_IEConfig(BSP_UART_ID, UARTDMA_RX_ERR_IE, ERN_ENABLE);

    LL_UART_DMA_CtlCmd(BSP_UART_ID, UARTDMA_RX_CYC_MODE, ERN_ENABLE);
    LL_UART_DMA_CtlCmd(BSP_UART_ID, UARTDMA_TX_CYC_MODE, ERN_ENABLE);
    LL_UART_DMA_CtlCmd(BSP_UART_ID, UARTDMA_RX_DMA_EN, ERN_ENABLE);
    LL_UART_DMA_CtlCmd(BSP_UART_ID, UARTDMA_TX_DMA_EN, ERN_ENABLE);

#endif
#endif
#if !defined(LL_UART_PRING)
    while(1)
    {
#if (BSP_TRANS_MODE == BSP_DELAY_SEND)
        BSP_UART_DelaySend();
        printf("Delay Send Done\n");
        SystemDelayUs(1000U);
#elif (BSP_TRANS_MODE == BSP_DELAY_RECV)
#if defined(UART_DEMO_RN8209)
        ugRN8209_ComBuf[1].u32Data = 0;
        if(ERN_SUCCESS == Bsp_RN8209_Read(RN8209_DeviceID, (uint8_t *)&ugRN8209_ComBuf[1], 3))
        {
            printf("device_id %x, %x, %x, %x \n", ugRN8209_ComBuf[1].u8Data[0], ugRN8209_ComBuf[1].u8Data[1], ugRN8209_ComBuf[1].u8Data[2], ugRN8209_ComBuf[1].u8Data[3]);
        }
        SystemDelayUs(1000U);
#else
        BSP_UART_DelayRecv((sTrans_TypeDef *)&gsTrans);
        printf("Delay Received %x\n", *(gsTrans.pu8Recv));
        SystemDelayUs(1000U);
#endif

        /* 其他系列产品UART 不支持DMA操作 */
#elif (BSP_TRANS_MODE == BSP_DMA_SEND)
#if defined(RN821x_RN721x_SOC_V2) || defined(RN831x_RN861x_MCU_V2) || defined(RN821x_RN721x_SOC_V3) || defined(RN831x_RN861x_MCU_V3) || defined(RN202x_RN7326_SOC_B) || \
    defined(RN202x_RN7326_SOC_V2)
        BSP_UART_DmaSend();
#endif
        printf("DMA Send Done\n");
        SystemDelayUs(1000U);

#elif (BSP_TRANS_MODE == BSP_DMA_RECV)
#if defined(RN821x_RN721x_SOC_V2) || defined(RN831x_RN861x_MCU_V2) || defined(RN821x_RN721x_SOC_V3) || defined(RN831x_RN861x_MCU_V3) || defined(RN202x_RN7326_SOC_B) || \
    defined(RN202x_RN7326_SOC_V2)
        gsTrans.u16RecvLen = BSP_UART_DmaRecv(start_cmd);
#endif
        for(u8i = 0; u8i < gsTrans.u16RecvLen; u8i++)
        {
            printf("DMA Received data %x\n", gu8UartRecvBuf[u8i]);
            *start_cmd = 1;
        }
        SystemDelayUs(1000U);
#endif
        WDT->EN = 0XBB;
        if(0x32 == gu8UartRecvBuf[0]) { gu8UartRecvBuf[0] = 0; }
    }
#endif
}

#endif

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
