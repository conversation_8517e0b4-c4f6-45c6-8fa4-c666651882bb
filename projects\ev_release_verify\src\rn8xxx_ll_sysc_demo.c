/**
 *******************************************************************************
 * @file  template/source/main.c
 * @brief Main program template for the Device Driver Library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-09-08       XT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "main.h"
#ifdef LL_SYSC_DEMO
#include <rn8xxx_ll_sysc_demo.h>


/**
 * @addtogroup LL_Templates
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 * @brief  Main function of template project
 * @param  None
 * @retval int32_t return value, if needed
 */

eLL_SYSC_RST_Status_TypeDef egRST_Status;
uint8_t gu8Test_Flag = 0;
void LL_SYSC_Exe_Demo(void)
{    
    while (1)
    {
#if defined(RN821x_RN721x_SOC_B) || defined(RN821x_RN721x_SOC_C) || defined(RN821x_RN721x_SOC_D)
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_TC_ID = (0),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_CPC_ID = (3),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_TC0_ID = (4),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_TC1_ID = (5),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART4_ID = (6),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART5_ID = (7),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART0_ID = (8),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART1_ID = (9),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART2_ID = (10),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART3_ID = (11),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART38K_ID = (12),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_ISO7816_ID = (13),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_I2C_ID = (14),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SPI0_ID = (15),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SIMPTC_ID = (20),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_DMA0_ID = (32),    /* mode1 0 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_DMA1_ID = (33),    /* mode1 1 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SPI1_ID = (35),    /* mode1 3 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_DMA_ID = (36),     /* mode1 4 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_GPIO_ID = (37),    /* mode1 5 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_LCD_ID = (38),     /* mode1 6 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_EMU_ID = (39),     /* mode1 7 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_NVM_ID = (40),     /* mode1 8 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_WDT_ID = (41),     /* mode1 9 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_RTC_ID = (42),     /* mode1 10 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SAR_ID = (43),     /* mode1 11 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_NVM_REG_ID = (44), /* mode1 12 + 32 */

        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC0_ID = (64), /* INT_EN 0 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC1_ID = (65), /* INT_EN 1 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC2_ID = (66), /* INT_EN 2 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC3_ID = (67), /* INT_EN 3 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC4_ID = (68), /* INT_EN 4 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC5_ID = (69), /* INT_EN 5 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC6_ID = (70), /* INT_EN 6 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC7_ID = (71), /* INT_EN 7 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC8_ID = (72), /* INT_EN 8 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI0_ID = (96),  /* KBI_EN 0 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI1_ID = (97),  /* KBI_EN 1 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI2_ID = (98),  /* KBI_EN 2 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI3_ID = (99),  /* KBI_EN 3 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI4_ID = (100), /* KBI_EN 4 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI5_ID = (101), /* KBI_EN 5 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI6_ID = (102), /* KBI_EN 6 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI7_ID = (103), /* KBI_EN 7 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI8_ID = (104), /* KBI_EN 8 + 96*/

#elif defined(RN821x_RN721x_SOC_V2)

        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_TC0_ID = (4),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_TC1_ID = (5),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART4_ID = (6),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART5_ID = (7),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART0_ID = (8),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART1_ID = (9),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART2_ID = (10),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART3_ID = (11),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_UART38K_ID = (12),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_ISO7816_ID = (13),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_I2C_ID = (14),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SPI0_ID = (15),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SPI2_ID = (17),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SPI3_ID = (18),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SIMPTC_ID = (20),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_LPUART_ID = (25),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_CRC_ID = (26),
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SPI1_ID = (35),    /* mode1 3 + 32 = 35 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_GPIO_ID = (37),    /* mode1 5 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_LCD_ID = (38),     /* mode1 6 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_EMU_ID = (39),     /* mode1 7 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_NVM_ID = (40),     /* mode1 8 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_WDT_ID = (41),    /*  mode1 9 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_RTC_ID = (42),     /* mode1 10 + 32  */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_SAR_ID = (43),     /* mode1 11 + 32  */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_NVMREG_ID = (44), /* mode1 12 + 32 */
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_D2F_ID = (45), /* mode1 13 + 32*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_DSP_ID = (46), /* mode1 14 + 32*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_M2M_ID = (47), /* mode1 15 + 32*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_FLK_ID = (48), /* mode1 16 + 32*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_IOCNT_ID = (49), /* mode1 17 + 32*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_ECT_ID = (50), /* mode1 18 + 32*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC0_ID = (64), /* INT_EN 0 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC1_ID = (65), /* INT_EN 1 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC2_ID = (66), /* INT_EN 2 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC3_ID = (67), /* INT_EN 3 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC4_ID = (68), /* INT_EN 4 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC5_ID = (69), /* INT_EN 5 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC6_ID = (70), /* INT_EN 6 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC7_ID = (71), /* INT_EN 7 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_INTC8_ID = (72), /* INT_EN 8 + 64*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); //
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI0_ID = (96),  /* KBI_EN 0 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI1_ID = (97),  /* KBI_EN 1 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI2_ID = (98),  /* KBI_EN 2 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI3_ID = (99),  /* KBI_EN 3 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI4_ID = (100), /* KBI_EN 4 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI5_ID = (101), /* KBI_EN 5 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI6_ID = (102), /* KBI_EN 6 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI7_ID = (103), /* KBI_EN 7 + 96*/
        LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_KBI8_ID = (104), /* KBI_EN 8 + 96*/

#elif defined RN831x_RN861x_MCU_V2

        // LL_SYSC_ApbClkCtrl(LL_SYSC_TC0_ID, ERN_ENABLE);     // LL_SYSC_TC0_ID = (4),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_TC1_ID, ERN_ENABLE);     // LL_SYSC_TC1_ID = (5),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART4_ID, ERN_ENABLE);   // LL_SYSC_UART4_ID = (6),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART5_ID, ERN_ENABLE);   // LL_SYSC_UART5_ID = (7),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART0_ID, ERN_ENABLE);   // LL_SYSC_UART0_ID = (8),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART1_ID, ERN_ENABLE);   // LL_SYSC_UART1_ID = (9),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART2_ID, ERN_ENABLE);   // LL_SYSC_UART2_ID = (10),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART3_ID, ERN_ENABLE);   // LL_SYSC_UART3_ID = (11),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART38K_ID, ERN_ENABLE); // LL_SYSC_UART38K_ID = (12),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_ISO7816_ID, ERN_ENABLE); // LL_SYSC_ISO7816_ID = (13),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_I2C_ID, ERN_ENABLE);     // LL_SYSC_I2C_ID = (14),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI_ID, ERN_ENABLE);     // LL_SYSC_SPI_ID = (15),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI1_ID, ERN_ENABLE);    // LL_SYSC_SPI1_ID = (16),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI2_ID, ERN_ENABLE);    // LL_SYSC_SPI2_ID = (17),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI3_ID, ERN_ENABLE);    // LL_SYSC_SPI3_ID = (18),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID, ERN_ENABLE);  // LL_SYSC_SIMPTC_ID = (20),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE); // LL_SYSC_GPIO_ID = (37),    /* mode1 5 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_LCD_ID, ERN_ENABLE);  // LL_SYSC_LCD_ID = (38),     /* mode1 6 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_WDT_ID, ERN_ENABLE);    // LL_SYSC_WDT_ID = (41),     /* mode1 9 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_RTCSAR_ID, ERN_ENABLE); // LL_SYSC_RTCSAR_ID = (42),    /* mode1 10 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_CMPLVD_ID, ERN_ENABLE); // LL_SYSC_CMPLVD_ID = (43),    /* mode1 11 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_D2F_ID, ERN_ENABLE); // LL_SYSC_D2F_ID = (45), /* mode1 13 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_DSP_ID, ERN_ENABLE); // LL_SYSC_DSP_ID = (46), /* mode1 14 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_M2M_ID, ERN_ENABLE); // LL_SYSC_M2M_ID = (47), /* mode1 15 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC0_ID, ERN_ENABLE);   // LL_SYSC_INTC0_ID = (64), /* INT_EN 0 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC1_ID, ERN_ENABLE); // LL_SYSC_INTC1_ID = (65), /* INT_EN 1 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC2_ID, ERN_ENABLE); // LL_SYSC_INTC2_ID = (66), /* INT_EN 2 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC3_ID, ERN_ENABLE); // LL_SYSC_INTC3_ID = (67), /* INT_EN 3 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC4_ID, ERN_ENABLE); // LL_SYSC_INTC4_ID = (68), /* INT_EN 4 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC5_ID, ERN_ENABLE); // LL_SYSC_INTC5_ID = (69), /* INT_EN 5 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC6_ID, ERN_ENABLE); // LL_SYSC_INTC6_ID = (70), /* INT_EN 6 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC7_ID, ERN_ENABLE); // LL_SYSC_INTC7_ID = (71), /* INT_EN 7 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC8_ID, ERN_ENABLE); // LL_SYSC_INTC8_ID = (72), /* INT_EN 8 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI0_ID, ERN_ENABLE); // LL_SYSC_KBI0_ID = (96),  /* KBI_EN 0 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI1_ID, ERN_ENABLE); // LL_SYSC_KBI1_ID = (97),  /* KBI_EN 1 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI2_ID, ERN_ENABLE); // LL_SYSC_KBI2_ID = (98),  /* KBI_EN 2 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI3_ID, ERN_ENABLE); // LL_SYSC_KBI3_ID = (99),  /* KBI_EN 3 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI4_ID, ERN_ENABLE); // LL_SYSC_KBI4_ID = (100), /* KBI_EN 4 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI5_ID, ERN_ENABLE); // LL_SYSC_KBI5_ID = (101), /* KBI_EN 5 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI6_ID, ERN_ENABLE); // LL_SYSC_KBI6_ID = (102), /* KBI_EN 6 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI7_ID, ERN_ENABLE); // LL_SYSC_KBI7_ID = (103), /* KBI_EN 7 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI8_ID, ERN_ENABLE); // LL_SYSC_KBI8_ID = (104), /* KBI_EN 8 + 96*/

        // LL_SYSC_ApbClkCtrl(LL_SYSC_TC0_ID, ERN_DISABLE);     // LL_SYSC_TC0_ID = (4),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_TC1_ID, ERN_DISABLE);     // LL_SYSC_TC1_ID = (5),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART4_ID, ERN_DISABLE);   // LL_SYSC_UART4_ID = (6),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART5_ID, ERN_DISABLE);   // LL_SYSC_UART5_ID = (7),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART0_ID, ERN_DISABLE);   // LL_SYSC_UART0_ID = (8),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART1_ID, ERN_DISABLE);   // LL_SYSC_UART1_ID = (9),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART2_ID, ERN_DISABLE);   // LL_SYSC_UART2_ID = (10),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART3_ID, ERN_DISABLE);   // LL_SYSC_UART3_ID = (11),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_UART38K_ID, ERN_DISABLE); // LL_SYSC_UART38K_ID = (12),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_ISO7816_ID, ERN_DISABLE); // LL_SYSC_ISO7816_ID = (13),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_I2C_ID, ERN_DISABLE);     // LL_SYSC_I2C_ID = (14),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI_ID, ERN_DISABLE);     // LL_SYSC_SPI_ID = (15),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI1_ID, ERN_DISABLE);    // LL_SYSC_SPI1_ID = (16),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI2_ID, ERN_DISABLE);    // LL_SYSC_SPI2_ID = (17),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SPI3_ID, ERN_DISABLE);    // LL_SYSC_SPI3_ID = (18),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID, ERN_DISABLE);  // LL_SYSC_SIMPTC_ID = (20),
        // LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_DISABLE); // LL_SYSC_GPIO_ID = (37),    /* mode1 5 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_LCD_ID, ERN_DISABLE);  // LL_SYSC_LCD_ID = (38),     /* mode1 6 + 32 */
        // //        LL_SYSC_ApbClkCtrl(LL_SYSC_WDT_ID, ERN_DISABLE);    // LL_SYSC_WDT_ID = (41),     /* mode1 9 + 32 请不要关闭WDT时钟*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_RTCSAR_ID, ERN_DISABLE); // LL_SYSC_RTCSAR_ID = (42),    /* mode1 10 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_CMPLVD_ID, ERN_DISABLE); // LL_SYSC_CMPLVD_ID = (43),    /* mode1 11 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_D2F_ID, ERN_DISABLE); // LL_SYSC_D2F_ID = (45), /* mode1 13 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_DSP_ID, ERN_DISABLE); // LL_SYSC_DSP_ID = (46), /* mode1 14 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_M2M_ID, ERN_DISABLE); // LL_SYSC_M2M_ID = (47), /* mode1 15 + 32 */
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC0_ID, ERN_DISABLE);   // LL_SYSC_INTC0_ID = (64), /* INT_EN 0 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC1_ID, ERN_DISABLE); // LL_SYSC_INTC1_ID = (65), /* INT_EN 1 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC2_ID, ERN_DISABLE); // LL_SYSC_INTC2_ID = (66), /* INT_EN 2 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC3_ID, ERN_DISABLE); // LL_SYSC_INTC3_ID = (67), /* INT_EN 3 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC4_ID, ERN_DISABLE); // LL_SYSC_INTC4_ID = (68), /* INT_EN 4 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC5_ID, ERN_DISABLE); // LL_SYSC_INTC5_ID = (69), /* INT_EN 5 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC6_ID, ERN_DISABLE); // LL_SYSC_INTC6_ID = (70), /* INT_EN 6 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC7_ID, ERN_DISABLE); // LL_SYSC_INTC7_ID = (71), /* INT_EN 7 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_INTC8_ID, ERN_DISABLE); // LL_SYSC_INTC8_ID = (72), /* INT_EN 8 + 64*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI0_ID, ERN_DISABLE); // LL_SYSC_KBI0_ID = (96),  /* KBI_EN 0 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI1_ID, ERN_DISABLE); // LL_SYSC_KBI1_ID = (97),  /* KBI_EN 1 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI2_ID, ERN_DISABLE); // LL_SYSC_KBI2_ID = (98),  /* KBI_EN 2 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI3_ID, ERN_DISABLE); // LL_SYSC_KBI3_ID = (99),  /* KBI_EN 3 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI4_ID, ERN_DISABLE); // LL_SYSC_KBI4_ID = (100), /* KBI_EN 4 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI5_ID, ERN_DISABLE); // LL_SYSC_KBI5_ID = (101), /* KBI_EN 5 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI6_ID, ERN_DISABLE); // LL_SYSC_KBI6_ID = (102), /* KBI_EN 6 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI7_ID, ERN_DISABLE); // LL_SYSC_KBI7_ID = (103), /* KBI_EN 7 + 96*/
        // LL_SYSC_ApbClkCtrl(LL_SYSC_KBI8_ID, ERN_DISABLE); // LL_SYSC_KBI8_ID = (104), /* KBI_EN 8 + 96*/

        
        egRST_Status = LL_SYSC_SysRstFlagGet(LL_SYSC_RSTFLAG_POWEROK);//LL_SYSC_RSTFLAG_POWEROK = (5),
        egRST_Status = LL_SYSC_SysRstFlagGet(LL_SYSC_RSTFLAG_PIN);//LL_SYSC_RSTFLAG_PIN = (6),
        egRST_Status = LL_SYSC_SysRstFlagGet(LL_SYSC_RSTFLAG_WDT);//LL_SYSC_RSTFLAG_WDT = (7),
        egRST_Status = LL_SYSC_SysRstFlagGet(LL_SYSC_RSTFLAG_MCU);//LL_SYSC_RSTFLAG_MCU =(8),

        if(gu8Test_Flag&0x2)
        {
            LL_SYSC_SysRstCfg(LL_SYSC_RSTCONFG_LOCKUP);
        }

        if(gu8Test_Flag&0x1)
        {
            LL_SYSC_SysRstFlagClr(LL_SYSC_RSTFLAG_POWEROK);
            LL_SYSC_SysRstFlagClr(LL_SYSC_RSTFLAG_MCU);
        }
          
        //LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI0,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_CACHE = (0),
        //LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_EMU,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_EMU = (1),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI0,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_SPI0 = (2),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI1,LL_SYSC_DMA_PRI2);//LL_SYSC_DMA_CHN_SPI1 = (3),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI2,LL_SYSC_DMA_PRI3);//LL_SYSC_DMA_CHN_SPI2 = (4),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI3,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_SPI3 = (5),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_UART0,LL_SYSC_DMA_PRI2);//LL_SYSC_DMA_CHN_UART0 = (6),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_UART1,LL_SYSC_DMA_PRI3);//LL_SYSC_DMA_CHN_UART1 = (7),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_UART2,LL_SYSC_DMA_PRI0);//LL_SYSC_DMA_CHN_UART2 = (8),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_UART3,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_UART3 = (9),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_UART4,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_UART4 = (10),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_UART5,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_UART5 = (11),
        //LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI0,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_CAN = (12),
        //LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI0,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_TC1 = (13),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_DSP,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_DSP = (14 + 2),
        //LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_SPI0,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_CRC = (15 + 2),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_M2M,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_M2M = (16 + 2),
        LL_SYSC_DMAPriSet(LL_SYSC_DMA_CHN_CPU,LL_SYSC_DMA_PRI1);//LL_SYSC_DMA_CHN_CPU = (17 + 2)          
#endif
    }
}

#endif

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
