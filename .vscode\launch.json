{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "cspy",
            "request": "attach",
            "name": "RN8211B-V3",
            "target": "arm",
            "program": "${workspaceFolder}\\projects\\ev_release_verify\\iar\\Debug\\Exe\\rn821x_rn721x_soc.out",
            "driver": "cmsisdap",  // 修改为CMSIS-DAP驱动
            "workbenchPath": "${command:iar-config.toolchain}",
            "projectPath": "${workspaceFolder}\\projects\\ev_release_verify\\iar\\rn821x_rn721x_soc.ewp",
            "projectConfiguration": "Debug",
            "driverOptions": [
                "--crun=disabled",
                "--endian=little",
                "--cpu=Cortex-M0",
                "--fpu=None",
                "-p",
                "${workspaceFolder}\\drivers\\rn_pcfg\\rn821x_rn721x_soc\\iar\\debugger\\Renergy\\RN8213B_SOC_V2.ddf",
                "--drv_verify_download",
                "--semihosting",
                "--device=RN8211_SOC_B",
                "--multicore_nr_of_cores=1",
                "--jet_probe=cmsisdap",
                "--jet_standard_reset=4,0,0",
                "--drv_communication_log=${workspaceFolder}\\projects\\ev_release_verify\\iar\\cspycomm.log",
                "--drv_interface_speed=4000",  // 调整接口速度
                "--drv_interface=SWD",  // 保持SWD接口
                "--drv_catch_exceptions=0xff0"
            ],
            "buildBeforeDebugging": "Disabled",
            "setupMacros": [
                "${workspaceFolder}\\drivers\\rn_pcfg\\rn821x_rn721x_soc\\iar\\flashloader\\Renergy\\RN8xxx.mac"
            ],
            "download": {
                "extraImages": [],
                "deviceMacros": [],
                "flashLoader": "${workspaceFolder}\\drivers\\rn_pcfg\\rn821x_rn721x_soc\\iar\\flashloader\\Renergy\\RN8213B_SOC_V2.board"
            }
        }
    ]
}