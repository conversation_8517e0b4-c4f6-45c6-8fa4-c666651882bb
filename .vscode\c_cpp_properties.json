{
    "configurations": [
        {
            "name": "RN8211B-V3",
            "includePath": [
                "${workspaceFolder}/projects/**",
                "${workspaceFolder}/drivers/cmsis/**",
                "${workspaceFolder}/drivers/rn8xxx_ll/**",
                "${workspaceFolder}/drivers/rn8xxx_ll/src/**",
                "${workspaceFolder}/drivers/rn8xxx_v2_sea_lib/**",
            ],
            "browse": {
                "path": [
                    //"C:/Program Files/IAR Systems/Embedded Workbench 9.2/arm/inc/c",
                    "${workspaceFolder}/projects",
                    "${workspaceFolder}/drivers/cmsis",
                    "${workspaceFolder}/drivers/rn8xxx_ll",
                    "${workspaceFolder}/drivers/rn8xxx_ll/src",
                    "${workspaceFolder}/drivers/rn8xxx_v2_sea_lib"
                ],
                "limitSymbolsToIncludedHeaders": true,
                "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"
            },
            "defines": [
                "USE_IAR",
                "__IAR",
                "RN821x_RN721x_SOC_V2"
            ],
            "windowsSdkVersion": "10.0.22621.0",
            "compilerPath": "cl.exe",
            "cStandard": "c99",
            "cppStandard": "c++11",
            "intelliSenseMode": "windows-msvc-x64",
            "configurationProvider": "iarsystems.iar-build"
        }
    ],
    "version": 4
}