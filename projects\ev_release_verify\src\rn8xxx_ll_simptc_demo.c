#include "main.h"
#ifdef LL_SMTC_DEMO
#include "rn8xxx_ll_simptc_demo.h"

//#define SIMP_TC0_DEMO
//#define SIMP_TC1_DEMO
//#define SIMP_TC2_DEMO
#define SIMP_TC3_DEMO

#if defined(SIMP_TC0_DEMO) 
#if defined(RN202x_RN7326_SOC_V2)
void SYSCLKCAL_HANDLER(void)
#else
void EXT1_HANDLER(void)
#endif
{
    if(GET_BIT_SIMPTC_CTRL(SIMP_TC0, 0x02) == 1)
    {
        SET_BIT_SIMPTC_CTRL(SIMP_TC0, 0x02);
    }
  
}
#endif

#if defined(SIMP_TC1_DEMO)
#if defined(RN202x_RN7326_SOC_V2)
void MADC_HANDLER(void)
#else
void EXT2_HANDLER(void)
#endif
{
    if(GET_BIT_SIMPTC_CTRL(SIMP_TC1, 0x02) == 1)
    {
        SET_BIT_SIMPTC_CTRL(SIMP_TC1, 0x02);
    }
  
}

#endif

#if defined(SIMP_TC2_DEMO)
#if defined(RN202x_RN7326_SOC_V2)
void ISO78161_HANDLER(void)
#else
void EXT3_HANDLER(void)
#endif
{
    if(GET_BIT_SIMPTC_CTRL(SIMP_TC2, 0x02) == 1)
    {
        SET_BIT_SIMPTC_CTRL(SIMP_TC2, 0x02);
    }
  
}
#endif

#if defined(SIMP_TC3_DEMO)
#if defined(RN202x_RN7326_SOC_V2)
void WDT_HANDLER(void)
#else
void EXT4_HANDLER(void)
#endif
{
    if(GET_BIT_SIMPTC_CTRL(SIMP_TC3, 0x02) == 1)
    {
        SET_BIT_SIMPTC_CTRL(SIMP_TC3, 0x02);
    }
  
}
#endif

void LL_SMTC_Exe_Demo(void)
{
    uint8_t smtc_test = 0;
    
    sLL_SIMPTC_InitTypeDef simptcCfg;
    simptcCfg.Ctrl.bitSimpTcCtrl.EN = SIMPTC_CTRL_DIS;/* 计数器使能位，初始化时先不开启 */
    simptcCfg.Ctrl.bitSimpTcCtrl.OV = SIMPTC_CTRL_OV_EN;/*计数器溢出标志位，写1清零 */
    simptcCfg.Ctrl.bitSimpTcCtrl.MODE = SIMPTC_CTRL_MODE_EN;/* 循环计数 */
    simptcCfg.Ctrl.bitSimpTcCtrl.IRQEN = SIMPTC_CTRL_IRQ_EN;/* 使能中断 */
    simptcCfg.mS = 1;
    simptcCfg.uS = 0;
#if defined(SIMP_TC0_DEMO)    
    LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID,ERN_ENABLE);
    LL_SIMPTC_Init(SIMP_TC0,&simptcCfg);
    LL_SIMPTC_START(SIMP_TC0);
    #if defined(RN202x_RN7326_SOC_V2)
    NVIC_EnableIRQ(SYSCLKCAL_IRQn);/* 开启中断 */
    #else
    NVIC_EnableIRQ(EXT1_IRQn);/* 开启中断 */
    #endif    
#endif
#if defined(SIMP_TC1_DEMO)    
    LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID,ERN_ENABLE);
    LL_SIMPTC_Init(SIMP_TC1,&simptcCfg);
    LL_SIMPTC_START(SIMP_TC1);
    #if defined(RN202x_RN7326_SOC_V2)
    NVIC_EnableIRQ(MADC_IRQn);/* 开启中断 */
    #else
    NVIC_EnableIRQ(EXT2_IRQn);/* 开启中断 */
    #endif    
#endif 
#if defined(SIMP_TC2_DEMO)    
    LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID,ERN_ENABLE);
    LL_SIMPTC_Init(SIMP_TC2,&simptcCfg);
    LL_SIMPTC_START(SIMP_TC2);
    #if defined(RN202x_RN7326_SOC_V2)
    NVIC_EnableIRQ(ISO78161_IRQn);/* 开启中断 */
    #else
    NVIC_EnableIRQ(EXT3_IRQn);/* 开启中断 */
    #endif    
#endif 
#if defined(SIMP_TC3_DEMO)    
    LL_SYSC_ApbClkCtrl(LL_SYSC_SIMPTC_ID,ERN_ENABLE);
    LL_SIMPTC_Init(SIMP_TC3,&simptcCfg);
    LL_SIMPTC_START(SIMP_TC3);
    #if defined(RN202x_RN7326_SOC_V2)
    NVIC_EnableIRQ(WDT_IRQn);/* 开启中断 */
    #else
    NVIC_EnableIRQ(EXT4_IRQn);/* 开启中断 */
    #endif    
#endif 
    if(1 == smtc_test)
    {
        LL_SIMPTC_Disable(SIMP_TC0);
            
        GET_REG_SIMPTC_CTRL(SIMP_TC0, 0x01);
        GET_REG_SIMPTC_LOAD(SIMP_TC0, 0x01);
        GET_REG_SIMPTC_VAL(SIMP_TC0, 0x01);
        SET_REG_SIMPTC_CTRL(SIMP_TC0, 0x01);
        SET_REG_SIMPTC_LOAD(SIMP_TC0, 0x01);
        GET_BIT_SIMPTC_CTRL(SIMP_TC0, 0x01);
        SET_BIT_SIMPTC_CTRL(SIMP_TC0, 0x01);
        CLR_BIT_SIMPTC_CTRL(SIMP_TC0, 0x01);    
        
        LL_SIMPTC_STOP(SIMP_TC0);
        LL_SIMPTC_IRQ_DIS(SIMP_TC0);
        LL_SIMPTC_IRQ_EN(SIMP_TC0);
    }
    
    
}
#endif
