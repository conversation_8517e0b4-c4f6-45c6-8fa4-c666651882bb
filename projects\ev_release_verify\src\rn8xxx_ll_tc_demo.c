#include "main.h"
#ifdef LL_TC_DEMO
#include "rn8xxx_ll_tc.h"

#if !defined(RN831x_RN861x_MCU_V3)
#define TEST_TC1_PWM
#define TEST_TC1_TIMER
#endif

#if defined(RN821x_RN721x_SOC_D) || defined(RN821x_RN721x_SOC_C) || defined(RN821x_RN721x_SOC_B)     
#define TEST_TC0_TIMER
#endif

void LL_TC_Exe_Demo(void)
{
    uint8_t tc_test = 0;

#ifdef TEST_TC1_PWM
    uTimerCfg_TypeDef timPwmCfgTemp;

    timPwmCfgTemp.bitTimerCfg.channele = 0;
    timPwmCfgTemp.bitTimerCfg.CM_DFTLVL = TC_Low_Level;
    timPwmCfgTemp.bitTimerCfg.CM_EFELVL = TC_High_Level;
    timPwmCfgTemp.bitTimerCfg.CM_OM = TC_OM_MODE_Pwm2;
    timPwmCfgTemp.bitTimerCfg.CM_CCM = TC_CCM_Compare;
    timPwmCfgTemp.bitTimerCfg.CM_DL = 60;
    timPwmCfgTemp.bitTimerCfg.CM_DIEN = TC_DIEN_Enable; 
    timPwmCfgTemp.bitTimerCfg.CM_Enalbe = TC_Enable;
    timPwmCfgTemp.bitTimerCfg.CTRL_Start = TC_Start;

    timPwmCfgTemp.bitTimerCfg.IE_CC1IE = 0;
    timPwmCfgTemp.bitTimerCfg.IE_CC0IE = 1;
    timPwmCfgTemp.bitTimerCfg.IE_OVIE = 0;

#if !defined(RN831x_RN861x_MCU_V3)
    LL_SYSC_ApbClkCtrl(LL_SYSC_TC1_ID,ERN_ENABLE);
    
    LL_TC_PwmOut_Init(TC1);
    LL_TC_PwmOut_SetConfig(TC1, 1000, 40, timPwmCfgTemp);
    LL_TC_PwmOut_Start(TC1, timPwmCfgTemp);
    
    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID,ERN_ENABLE);

    // LL_GPIO_CfgInit(PIN_5_2,                         /*!< IO口ID */
    //                  _TC1_N_0,           /*!< 复用模式 */
    //                  Low_Level, /*"输出电平选择"*/
    //                  Pull_OFF,               /*!< 上拉选择 */
    //                  GPIO_MODE_OUT,                 /*!< 输入输出选择 */
    //                  COMS_MODE,     /*!< 输入模式*/
    //                  PushPll_MODE);   /*!< 输出模式*/

    LL_GPIO_CfgInit(PIN_2_6,                         /*!< IO口ID */
                     _NORMALIO,           /*!< 复用模式 */
                     Low_Level, /*"输出电平选择"*/
                     Pull_OFF,               /*!< 上拉选择 */
                     GPIO_MODE_OUT,                 /*!< 输入输出选择 */
                     COMS_MODE,     /*!< 输入模式*/
                     PushPll_MODE);   /*!< 输出模式*/                 
LL_GPIO_OverturnPin(PIN_2_6);
LL_GPIO_OverturnPin(PIN_2_6);
#endif
#endif

#ifdef TEST_TC0_TIMER
    LL_SYSC_ApbClkCtrl(LL_SYSC_TC0_ID,ERN_ENABLE);
    LL_TC_Time_Init(TC0);
    LL_TC_Time_SetConfig(TC0, 100, ERN_ENABLE, ERN_ENABLE);//1ms定时器
    LL_TC_Time_Start(TC0);
#endif

#ifdef TEST_TC1_TIMER
    LL_TC_TimeUs_Init(TC1);
    LL_TC_TimeUs_SetConfig(TC1, 100, ERN_ENABLE, ERN_ENABLE);//1ms定时器
    LL_TC_TimeUs_Start(TC1);
#endif
      
//    BITBAND_ADDR(0x50000024, 10) = 1;
//    LL_GPIO_CfgFun(PIN_5_2,_TC1_N_0);
//    LL_GPIO_CfgPullMode(PIN_5_2, Pull_ON);
//    LL_GPIO_CfgFun(PIN_5_4,_TC1_N_0);
    

//    
//    if(1 == tc_test)
//    {
  //    LL_TC_SquareWave_Init(TC0);
//    LL_TC_SquareWave_SetConfig(TC0 , 0, 1000, ERN_ENABLE);
//    LL_TC_SquareWave_Start(TC0);
//        LL_TC_SquareWave_Stop(TC0);

//        LL_TC_Time_Init(TC0);
//        LL_TC_Time_SetConfig(TC0, 1000, ERN_ENABLE, ERN_ENABLE);
//        LL_TC_Time_Start(TC0);
//        LL_TC_Time_Stop(TC0);

//        LL_TC_TimeUs_Init(TC0);
//        LL_TC_TimeUs_SetConfig(TC0, 1000, ERN_ENABLE, ERN_ENABLE);
//        LL_TC_TimeUs_Start(TC0);
//        LL_TC_TimeUs_Stop(TC0);

//        LL_TC_PwmOut_Init(TC0);
//        LL_TC_PwmOut_SetConfig(TC0, 1000, 40, timPwmCfgTemp);
//        LL_TC_PwmOut_Start(TC0, timPwmCfgTemp);
//        LL_TC_PwmOut_Stop(TC0, timPwmCfgTemp);

//        LL_TC_PusleMes_Init(TC0);
//        LL_TC_PusleMes_SetConfig(TC0, 1000, timPwmCfgTemp);
//        LL_TC_PusleMes_Start(TC0, timPwmCfgTemp);
//        LL_TC_PusleMes_Stop(TC0, timPwmCfgTemp);
//        fnTimeMeasue(TC0);

//        LL_TC_Disable(TC0);

//        GET_REG_TC_CNT(TC0, 0x01);
//        GET_REG_TC_PS(TC0, 0x01);
//        GET_REG_TC_DN(TC0, 0x01);
//        GET_REG_TC_CCD0(TC0, 0x01);
//        GET_REG_TC_CCD1(TC0, 0x01);
//        GET_REG_TC_CCFG(TC0, 0x01);
//        GET_REG_TC_CTRL(TC0, 0x01);
//        GET_REG_TC_CM0(TC0, 0x01);
//        GET_REG_TC_CM1(TC0, 0x01);
//        GET_REG_TC_IE(TC0, 0x01);
//        GET_REG_TC_STA(TC0, 0x01);

//        SET_REG_TC_CNT(TC0, 0x01);
//        SET_REG_TC_PS(TC0, 0x01);
//        SET_REG_TC_DN(TC0, 0x01);
//        SET_REG_TC_CCD0(TC0, 0x01);
//        SET_REG_TC_CCD1(TC0, 0x01);
//        SET_REG_TC_CCFG(TC0, 0x01);
//        SET_REG_TC_CTRL(TC0, 0x01);
//        SET_REG_TC_CM0(TC0, 0x01);
//        SET_REG_TC_CM1(TC0, 0x01);
//        SET_REG_TC_IE(TC0, 0x01);
//        SET_REG_TC_STA(TC0, 0x01);

//        GET_BIT_TC_CNT(TC0, 0x01);
//        GET_BIT_TC_PS(TC0, 0x01);
//        GET_BIT_TC_DN(TC0, 0x01);
//        GET_BIT_TC_CCD0(TC0, 0x01);
//        GET_BIT_TC_CCD1(TC0, 0x01);
//        GET_BIT_TC_CCFG(TC0, 0x01);
//        GET_BIT_TC_CTRL(TC0, 0x01);
//        GET_BIT_TC_CM0(TC0, 0x01);
//        GET_BIT_TC_CM1(TC0, 0x01);
//        GET_BIT_TC_IE(TC0, 0x01);
//        GET_BIT_TC_STA(TC0, 0x01);

//        SET_BIT_TC_CNT(TC0, 0x01);
//        SET_BIT_TC_PS(TC0, 0x01);
//        SET_BIT_TC_DN(TC0, 0x01);
//        SET_BIT_TC_CCD0(TC0, 0x01);
//        SET_BIT_TC_CCD1(TC0, 0x01);
//        SET_BIT_TC_CCFG(TC0, 0x01);
//        SET_BIT_TC_CTRL(TC0, 0x01);
//        SET_BIT_TC_CM0(TC0, 0x01);
//        SET_BIT_TC_CM1(TC0, 0x01);
//        SET_BIT_TC_IE(TC0, 0x01);
//        SET_BIT_TC_STA(TC0, 0x01);
//    }
}

#endif
