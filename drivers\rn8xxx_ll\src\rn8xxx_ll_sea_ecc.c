/******************************************************************************
 * @file        rn8xxx_ll_sea_ecc.c
 * @brief       ecc cypher functions
 * <AUTHOR> Technology
 *
 * @note
 * Copyright (C) , Renergy Technology Inc. All rights reserved.
 ******************************************************************************/
#include "rn8xxx_ll_sea_common.h"
#include "rn8xxx_ll_sea_ecc.h"
#include "rn8xxx_ll_sea_trng.h"

#ifndef  RN821x_RN721x_SOC_V3

static void  SEA_ECC_IR_FUNC_RUN(uint8_t IR_FUNC, uint32_t TIMEOUT_CFG, ErrStatus *ERR_RET);
static ErrStatus ecc_cal_J0(const uint32_t J0[]);
ErrStatus ecc_cal_H(uint16_t num,const uint32_t P[]);
static ErrStatus ecc_dbl_i2m(uint8_t wnum, const uint32_t A[], const uint32_t Q0_X[], const uint32_t Q0_Y[]);
static void ecc_copy2Q1(uint8_t wnum);
static uint8_t ecc_numdigits(const uint32_t vli[], uint8_t max_words);
static ErrStatus ecc_point_double(void);
static ErrStatus ecc_m2i(void);
static int ecc_cmp_unsafe(const uint32_t left[], const uint32_t right[], uint8_t wnum);
static ErrStatus ecc_modadd(const uint32_t P[], const uint32_t X1[], const uint32_t X2[], uint8_t wnum, uint16_t bnum, uint32_t result[]);
#if 0
static ErrStatus ecc_modsub(const uint32_t P[],const uint32_t X1[],const uint32_t X2[], uint8_t wnum, uint16_t bnum,uint32_t result[]);
#endif
static ErrStatus ecc_point_add(void);
 
static ErrStatus ecc_point_mult(const uint32_t  P[],const uint32_t A[], const uint32_t  pkey[], const uint32_t X[], const uint32_t Y[], uint8_t wnum, uint16_t bnum
                     ,uint32_t resultX[], uint32_t resultY[]);
static ErrStatus ecc_modmult(const uint32_t P[], const uint32_t X1[], const uint32_t X2[], uint8_t wnum, uint16_t bnum, uint32_t result[]);
static ErrStatus ecc_point_oncurve(const uint32_t P[], const uint32_t A[], const uint32_t B[], const uint32_t X[], const uint32_t Y[],  uint8_t wnum,  uint16_t bnum, PassFlag* ver_flag);
static ErrStatus ecc_modinv(const uint32_t P[], const uint32_t U[], uint8_t wnum, uint16_t bnum, uint32_t result[], PassFlag *flag);
static ErrStatus ecc_add_i2m(uint8_t wnum, const uint32_t A[], const uint32_t Q0_X[], const uint32_t Q0_Y[], const uint32_t Q1_X[], const uint32_t Q1_Y[]);
static void ecc_infinity(const uint8_t wnum,PassFlag* infinity_status);

/******************************************************************************
 * Configuration of the Elliptic curve type
 * Allowed values:
 *     - NIST_P192 for a 192 bit ECC curve support NIST Standards
 *     - NIST_P224 for a 224 bit ECC curve support NIST Standards
 *     - NIST_P256 for a 256 bit ECC curve support NIST Standards
 *     - NIST_P384 for a 384 bit ECC curve support NIST Standards
 *     - NIST_P521 for a 521 bit ECC curve support NIST Standards
 *     -
 *     -
 *     -
 *****************************************************************************/
const uECC_Curve_t Curve_NIST_P192 = {
    6                                                                   /*num_words*/
  , 24                                                                  /*num_bytes*/
  , 192                                                                 /*num_bits */
  , {0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFEU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0U         ,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*p*/
  , {0xB4D22831U,0x146BC9B1U,0x99DEF836U,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0U         ,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*n*/
  , {0xFFFFFFFCU,0xFFFFFFFFU,0xFFFFFFFEU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0U         ,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*a*/
  , {0xC146B9B1U,0xFEB8DEECU,0x72243049U,0x0FA7E9ABU,0xE59C80E7U,0x64210519U,
     0U         ,0U        ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*b*/
  , {0x82FF1012U,0xF4FF0AFDU,0x43A18800U,0x7CBF20EBU,0xB03090F6U,0x188DA80EU,
     0U         ,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U                      /*Gx*/
  ,  0x1E794811U,0x73F977A1U,0x6B24CDD5U,0x631011EDU,0xFFC8DA78U,0x07192B95U,
     0U         ,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*Gy*/
};

const uECC_Curve_t Curve_NIST_P224 = {
    7                                                                   /*num_words*/
  , 28                                                                  /*num_bytes*/
  , 224                                                                 /*num_bits*/
  , {0x00000001U,0x00000000U,0x00000000U,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*p*/
  , {0x5C5C2A3DU,0x13DD2945U,0xE0B8F03EU,0xFFFF16A2U,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*n*/
  , {0xFFFFFFFEU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFEU,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*a*/
  , {0x2355FFB4U,0x270B3943U,0xD7BFD8BAU,0x5044B0B7U,0xF5413256U,0x0C04B3ABU,
     0xB4050A85U,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*b*/
  , {0x115C1D21U,0x343280D6U,0x56C21122U,0x4A03C1D3U,0x321390B9U,0x6BB4BF7FU,
     0xB70E0CBDU,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U                      /*Gx*/
  ,  0x85007E34U,0x44D58199U,0x5A074764U,0xCD4375A0U,0x4C22DFE6U,0xB5F723FBU,
     0xBD376388U,0U         ,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*Gy*/
};


const uECC_Curve_t Curve_NIST_P256 = {
    8                                                                   /*num_words*/
  , 32                                                                  /*num_bytes*/
  , 256                                                                 /*num_bits */
  , {0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0x00000000U,0x00000000U,0x00000000U,
     0x00000001U,0xFFFFFFFFU,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*p*/
  , {0xFC632551U,0xF3B9CAC2U,0xA7179E84U,0xBCE6FAADU,0xFFFFFFFFU,0xFFFFFFFFU,
     0x00000000U,0xFFFFFFFFU,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*n*/
  , {0xFFFFFFFCU,0xFFFFFFFFU,0xFFFFFFFFU,0x00000000U,0x00000000U,0x00000000U,
     0x00000001U,0xFFFFFFFFU,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*a*/
  , {0x27D2604BU,0x3BCE3C3EU,0xCC53B0F6U,0x651D06B0,0x769886BCU,0xB3EBBD55U,
     0xAA3A93E7U,0x5AC635D8U,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*b*/
  , {0xD898C296U,0xF4A13945U,0x2DEB33A0,0x77037D81U,0x63A440F2U,0xF8BCE6E5U,
     0xE12C4247U,0x6B17D1F2U,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U                      /*Gx*/
  ,  0x37BF51F5U,0xCBB64068U,0x6B315ECEU,0x2BCE3357U,0x7C0F9E16U,0x8EE7EB4AU,
     0xFE1A7F9BU,0x4FE342E2U,0U         ,0U         ,0U         ,0U         ,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*Gy*/
};

const uECC_Curve_t Curve_NIST_P384 = {
    12                                                                  /*num_words*/
  , 48                                                                  /*num_bytes*/
  , 384                                                                 /*num_bits */
  , {0xFFFFFFFFU,0x00000000U,0x00000000U,0xFFFFFFFFU,0xFFFFFFFEU,0xFFFFFFFFU,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*p*/
  , {0xCCC52973U,0xECEC196AU,0x48B0A77AU,0x581A0DB2U,0xF4372DDFU,0xC7634D81U,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*n*/
  , {0xFFFFFFFCU,0x00000000U,0x00000000U,0xFFFFFFFFU,0xFFFFFFFEU,0xFFFFFFFFU,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*a*/
  , {0xD3EC2AEFU,0x2A85C8EDU,0x8A2ED19DU,0xC656398DU,0x5013875AU,0x0314088FU,
     0xFE814112U,0x181D9C6EU,0xE3F82D19U,0x988E056BU,0xE23EE7E4U,0xB3312FA7U,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*b*/
  , {0x72760AB7U,0x3A545E38U,0xBF55296CU,0x5502F25DU,0x82542A38U,0x59F741E0,
     0x8BA79B98U,0x6E1D3B62U,0xF320AD74U,0x8EB1C71EU,0xBE8B0537U,0xAA87CA22U,
     0U         ,0U         ,0U         ,0U         ,0U                      /*Gx*/
  ,  0x90EA0E5FU,0x7A431D7CU,0x1D7E819DU,0x0A60B1CEU,0xB5F0B8C0U,0xE9DA3113U,
     0x289A147CU,0xF8F41DBDU,0x9292DC29U,0x5D9E98BFU,0x96262C6FU,0x3617DE4AU,
     0U         ,0U         ,0U         ,0U         ,0U         }            /*Gy*/
};


const uECC_Curve_t Curve_NIST_P521 = {
    17                                                                  /*num_words*/
  , 66                                                                  /*num_bytes*/
  , 521                                                                 /*num_bits */
  , {0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0x000001FFU}            /*p*/
  , {0x91386409U,0xBB6FB71EU,0x899C47AEU,0x3BB5C9B8U,0xF709A5D0U,0x7FCC0148U,
     0xBF2F966BU,0x51868783U,0xFFFFFFFAU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0x000001FFU}            /*n*/
  , {0xFFFFFFFCU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,
     0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0xFFFFFFFFU,0x000001FFU}            /*a*/
  , {0x6B503F00,0xEF451FD4U,0x3D2C34F1U,0x3573DF88U,0x3BB1BF07U,0x1652C0BDU,
     0xEC7E937BU,0x56193951U,0x8EF109E1U,0xB8B48991U,0x99B315F3U,0xA2DA725BU,
     0xB68540EEU,0x929A21A0U,0x8E1C9A1FU,0x953EB961U,0x00000051U}            /*b*/
  , {0xC2E5BD66U,0xF97E7E31U,0x856A429BU,0x3348B3C1U,0xA2FFA8DEU,0xFE1DC127U,
     0xEFE75928U,0xA14B5E77U,0x6B4D3DBAU,0xF828AF60U,0x053FB521U,0x9C648139U,
     0x2395B442U,0x9E3ECB66U,0x0404E9CDU,0x858E06B7U,0x000000C6U             /*Gx*/
  ,  0x9FD16650U,0x88BE9476U,0xA272C240U,0x353C7086U,0x3FAD0761U,0xC550B901U,
     0x5EF42640U,0x97EE7299U,0x273E662CU,0x17AFBD17U,0x579B4468U,0x98F54449U,
     0x2C7D1BD9U,0x5C8A5FB4U,0x9A3BC004U,0x39296A78U,0x00000118U}            /*Gy*/
};

#if 0
#define  SEA_ECC_IR_FUNC_RUN(IR_FUNC, TIMEOUT_CFG, ERR_RET)     \
         do{\
         uint32_t cnt = 0U;\
         SEA_CYPHER->CTRL = (IR_FUNC);\
         SEA_CYPHER->Status = 1U;\
         SEA_CYPHER->START  = 1U;\
         while((SEA_CYPHER->Status & 1U)==0U){\
            if(cnt++ > (TIMEOUT_CFG)){\
                (ERR_RET) = ERROR;\
                setErrorCode(ECC_TIMEOUT_FAILURE) ;\
                break  ;\
            }\
         }\
     }while(0U)
#else
static void  SEA_ECC_IR_FUNC_RUN(uint8_t IR_FUNC, uint32_t TIMEOUT_CFG, ErrStatus *ERR_RET)
{
    uint32_t cnt = 0U;
    SEA_CYPHER->CTRL = (IR_FUNC);
    SEA_CYPHER->Status = 1U;
    SEA_CYPHER->START  = 1U;
    while((SEA_CYPHER->Status & 1U)==0U){
        if(cnt++ > (TIMEOUT_CFG)){
            *(ERR_RET) = ERROR;
            setErrorCode(ECC_TIMEOUT_FAILURE) ;
            break  ;
        }
    }
}

#endif

/**
 * @brief ecc_cmp_unsafe
 * @param left the left num
 * @param right the right num
 * @param wnum the expected comparing length
 * 
 * @return 1:left>right; -1:left<right; 0:left=right
 */
static int ecc_cmp_unsafe(const uint32_t left[], const uint32_t right[], uint8_t wnum)
{
    uint8_t i = wnum -1U;
    int tmp_ret = 0;
    do
    {
      if(left[i] != right[i]){
        if(left[i] > right[i]){
          tmp_ret =   1;
        }
        else{
          tmp_ret =  -1;
        }
        break;
      }
    }while(0U != i--);
    return tmp_ret;
}

/**
 * @brief ecc_modadd
 * @param P modular value
 * @param X1 operand 1
 * @param X2 operand 2
 * @param wnum word length
 * @param bnum bit length
 *
 * @return result
 */
static ErrStatus ecc_modadd(const uint32_t P[], const uint32_t X1[], const uint32_t X2[], uint8_t wnum, uint16_t bnum, uint32_t result[])
{
    uint8_t i;
    ErrStatus tmp_err =  SUCCESS;

    if(wnum > 17U)
    {
        tmp_err = ERROR;
        setErrorCode(ECC_PARASLEN_FAILURE) ;
    }
    else
    {
        for( i = 0U; i < wnum; i++){
            SEA_ECC_MOD_P->REG[i]     = P[i];
            SEA_ECC_MOD_PT->REG[i]    = P[i];
            SEA_ECC_MODADD_ADDX->REG[i]  = X1[i];
            SEA_ECC_MODADD_ADDY->REG[i]  = X2[i];
        }
        /*64bit expand*/
        for(i = wnum; i < (wnum + 2U); i++){
            SEA_ECC_MODADD_ADDX->REG[i] = 0U;
            SEA_ECC_MODADD_ADDY->REG[i] = 0U;
        }

        SEA_CYPHER->NLen = bnum;

        SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_ModAdd, SEA_TIMEOUT_IR_ECC_ModAdd, &tmp_err);

        for( i = 0U; i < wnum; i++){
            result[i] = SEA_ECC_MODADD_ADDX->REG[i];
        }
     }
    return tmp_err;
}
#if 0
/**
 * @brief ecc_modsub
 * @param P modular value
 * @param X1 operand 1
 * @param X2 operand 2
 * @param wnum word length
 * @param bnum bit length
 * 
 * @return result
 */
ErrStatus ecc_modsub(const uint32_t P[],const uint32_t X1[],const uint32_t X2[], uint8_t wnum, uint16_t bnum,uint32_t result[])
{
    uint8_t i;
    ErrStatus tmp_err =  SUCCESS;
    if(wnum > 18U)
    {
        tmp_err = ERROR;
        setErrorCode(ECC_PARASLEN_FAILURE) ;
    }
    else
    {
        for( i = 0U; i < wnum; i++)
        {
            SEA_ECC_MOD_P->REG[i]     = P[i];
            SEA_ECC_MOD_PT->REG[i]    = P[i];
            SEA_ECC_MODADD_ADDX->REG[i]  = X1[i];
            SEA_ECC_MODADD_ADDY->REG[i]  = X2[i];
        }
        for(i = wnum; i < wnum +2U; i++)
        {
            SEA_ECC_MODADD_ADDX->REG[i] = 0U;
            SEA_ECC_MODADD_ADDY->REG[i] = 0U;
        }
        SEA_CYPHER->NLen = bnum;

        SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_ModSub, SEA_TIMEOUT_IR_ECC_ModSub, &tmp_err);

        for( i = 0U; i < wnum; i++)
        {
            result[i] = SEA_ECC_MODADD_ADDX->REG[i];
        }
    }
    return tmp_err;
}
#endif
/**
 * @brief ecc_cal_J0
 * @param J0 modular value low 64 bits
 *
 */
static ErrStatus ecc_cal_J0(const uint32_t J0[])
{
    ErrStatus tmp_err =  SUCCESS;

    SEA_CYPHER->N0Low  = J0[0];
    SEA_CYPHER->N0High = J0[1];

    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_PKA_J0_CAL, SEA_TIMEOUT_IR_PKA_J0_CAL, &tmp_err);

    return tmp_err;
}
/**
 * @brief ecc_cal_H
 * @param num modular length
 * @param P modular value
 * 
 */
ErrStatus ecc_cal_H(uint16_t num,const uint32_t P[])
{
    uint16_t temp;
    uint32_t i;
    ErrStatus tmp_err =  SUCCESS;

    for( i = 0U; i < ((uint32_t)num >> 5); i++)
    {
        SEA_ECC_MOD_P->REG[i] = P[i];
        SEA_ECC_MOD_PT->REG[i] = P[i];
        SEA_ECC_H->REG[i] = 0U;
    }
    temp = num & 0x1fU;

    SEA_ECC_H->REG[i] = (uint32_t)1U << (uint32_t)temp;

    if(0U != temp)
    {
        SEA_ECC_MOD_P->REG[i] = P[i];
        SEA_ECC_MOD_PT->REG[i] = P[i];
    }
    SEA_CYPHER->NLen = num;
    
    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_H_CAL, SEA_TIMEOUT_IR_ECC_H_CAL, &tmp_err);

    return tmp_err;
}
/**
 * @brief ecc_dbl_i2m
 * @param wnum word length
 * @param A curve parameter a
 * @param Q0_X vector Q0 x-coordinate
 * @param Q0_Y vector Q0 y-coordinate
 */
static ErrStatus ecc_dbl_i2m(uint8_t wnum,const uint32_t A[],const uint32_t Q0_X[], const uint32_t Q0_Y[])
{
    uint32_t i;
    ErrStatus tmp_err =  SUCCESS;

    SEA_ECC_MAP_CONST->REG[0] = 1U;
    SEA_ECC_MAP_Q0_Z->REG[0] = 1U;
    for(i = 1U; i < wnum; i++)
    {
        SEA_ECC_MAP_CONST->REG[i] = 0U;
        SEA_ECC_MAP_Q0_Z->REG[i] = 0U;
    }
    for(i = 0U; i < wnum; i++)
    {
        SEA_ECC_MAP_A->REG[i] = A[i];
        SEA_ECC_MAP_Q0_X->REG[i] = Q0_X[i];
        SEA_ECC_MAP_Q0_Y->REG[i] = Q0_Y[i];
    }
    
    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_DBL_I2M, SEA_TIMEOUT_IR_ECC_DBL_I2M, &tmp_err);

    return tmp_err;
}
/**
 * @brief ecc_copy2Q1
 * @param wnum word length
 */
static void ecc_copy2Q1(uint8_t wnum)
{
    uint8_t i;
    for(i = 0U; i < wnum; i++)
    {
        SEA_ECC_MAP_Q1_X->REG[i] = SEA_ECC_MAP_Q0_X->REG[i];
        SEA_ECC_MAP_Q1_Y->REG[i] = SEA_ECC_MAP_Q0_Y->REG[i];
        SEA_ECC_MAP_Q1_Z->REG[i] = SEA_ECC_MAP_Q0_Z->REG[i];
    }
}
/**
 * @brief ecc_numdigits
 * @param vli value
 * @param max_words value maximun word length
 * 
 */
static uint8_t ecc_numdigits(const uint32_t vli[], uint8_t max_words)
{
    uint8_t i = max_words - 1U ;
    do{
      if(vli[i] != 0U ){
        break;
      }
    }while(0U != i--);

    return i+1U;
}
/**
 * @brief ecc_point_double
 *
 */
static ErrStatus ecc_point_double(void)
{
    ErrStatus tmp_err =  SUCCESS;

    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_Point_DBL, SEA_TIMEOUT_IR_ECC_Point_DBL, &tmp_err);
 
    return tmp_err;
}
/**
 * @brief ecc_point_add
 *
 */
static ErrStatus ecc_point_add(void)
{
    ErrStatus tmp_err =  SUCCESS;
    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_Point_ADD, SEA_TIMEOUT_IR_ECC_Point_ADD, &tmp_err);
    return tmp_err;
}
/**
 * @brief ecc_m2i
 *
 */
static ErrStatus ecc_m2i(void)
{
    ErrStatus tmp_err =  SUCCESS;

    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_M2I, SEA_TIMEOUT_IR_ECC_M2I, &tmp_err);

    return tmp_err;
}
/**
 * @brief ecc_point_mult
 * @param P modular value
 * @param A curve parameter a
 * @param pkey scalar
 * @param X vector x-coordinate
 * @param Y vector y-coordinate
 * @param wnum word length
 * @param bnum bit length
 * 
 * @return resultX result vector x-coordinate
 * @return resultY result vector y-coordinate
 */
static ErrStatus ecc_point_mult(const uint32_t  P[], const uint32_t A[], const uint32_t  pkey[], const uint32_t X[], const uint32_t Y[], uint8_t wnum, uint16_t bnum
                    ,uint32_t resultX[], uint32_t resultY[])
{
    int len;
    int m;
    int i;
    uint32_t chk;
    uint32_t num_words;
    uint16_t num_bits;
    ErrStatus tmp_err =  SUCCESS;
 
    (void)ecc_cal_J0(P);
    (void)ecc_cal_H(bnum,P);
    (void)ecc_dbl_i2m(wnum,A,X,Y);
    ecc_copy2Q1(wnum);
    num_words = ecc_numdigits(pkey,wnum);
    
    chk = pkey[num_words - 1U];
    i = 0;
    while(0U != chk){
        i++;
        chk >>= 1U;
    }

    num_bits = (uint16_t)i;
    for(len = (int)num_words -1 ; len >= 0; len--)
    {
        if(len != ((int)num_words -1))
        {
            for(m = 31; m >= 0; m--)
            {
                if(SUCCESS != ecc_point_double()){
                  tmp_err = ERROR;
                }
                chk = pkey[len] & ((uint32_t)1U << (uint32_t)m);
                if(0U != chk){
                    (void)ecc_point_add();
                }
            }
        }
        else
        {
            for(m = (int)num_bits - 2; m >= 0; m--)
            {
                if(SUCCESS != ecc_point_double()){
                  tmp_err = ERROR;
                }
                chk = pkey[len] & ((uint32_t)1U << (uint32_t)m);
                if(0U != chk)
                {
                    (void)ecc_point_add();
                }
            }
        }
        if(SUCCESS != tmp_err){
          break;
        }
    }
    (void)ecc_m2i();
    for(i = 0; i < (int)wnum; i++)
    {
        resultX[i] = SEA_ECC_MAP_Q0_X->REG[i];
        resultY[i] = SEA_ECC_MAP_Q0_Y->REG[i];
    }
 
    return tmp_err;
}
/**
 * @brief ecc_modmult
 * @param P modular value
 * @param X1 the one
 * @param X2 the other 0ne
 * @param wnum word length
 * @param bnum bit length
 * 
 * @return result x*y mod P
 * 
 */
static ErrStatus ecc_modmult(const uint32_t P[], const uint32_t X1[], const uint32_t X2[], uint8_t wnum, uint16_t bnum, uint32_t result[])
{
    uint32_t i;
    ErrStatus tmp_err = SUCCESS;

    (void)ecc_cal_J0(P);
    (void)ecc_cal_H(bnum, P);
    
    for( i = 0U; i < wnum; i++)
    {
        SEA_ECC_MAP_Q0_X->REG[i] = X1[i];
        SEA_ECC_MAP_Q0_Y->REG[i] = X2[i];
    }

    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_ModMul, SEA_TIMEOUT_IR_ECC_ModMul, &tmp_err);

    for( i = 0U; i < wnum; i++)
    {
        result[i] = SEA_ECC_MAP_Q0_X->REG[i];
    }
    return  tmp_err;
}
/**
 * @brief ecc_point_oncurve
 * @param P modular value
 * @param A curve parameter a
 * @param B curve parameter b
 * @param X vector x-coordinate
 * @param Y vector y-coordinate
 * @param wnum word length
 * @param bnum bit length
 *
 * @return ver_flag: PASS-0, FALI-1
 */
static ErrStatus ecc_point_oncurve(const uint32_t P[], const uint32_t A[], const uint32_t B[], const uint32_t X[], const uint32_t Y[],  uint8_t wnum,  uint16_t bnum, PassFlag *ver_flag)
{
    uint32_t temp0[17],temp1[17],temp2[17];
    PassFlag tmp_flag = PASS;
    if(ecc_cmp_unsafe(X, P, wnum) >= 0)
    {
        tmp_flag = FAIL;
    }else{
      if(ecc_cmp_unsafe(Y,P,wnum) >= 0)
      {
          tmp_flag = FAIL;
      }
    }
    
    if(tmp_flag == PASS){
      (void)ecc_modmult(P, Y, Y,wnum, bnum,temp0);/*Y^2*/
      (void)ecc_modmult(P, X, X,wnum, bnum,temp1);
      (void)ecc_modmult(P, X, temp1,wnum,bnum,temp2);/*X^3*/
      (void)ecc_modmult(P, A, X, wnum,bnum,temp1);/*A*X*/
      (void)ecc_modadd(P,temp2,temp1,wnum,bnum,temp1);/*X^3 + A*X*/
      (void)ecc_modadd(P,temp1,B,wnum,bnum,temp2);/*X^3 + A*X +B*/
      if(0 != ecc_cmp_unsafe(temp0,temp2,wnum))
      {
          tmp_flag = FAIL;
      }
    }
    *ver_flag = tmp_flag;
    return SUCCESS;
}

/**
 * @brief ecc_modinv
 * @param P modular value
 * @param X value
 * @param wnum word length
 * @param bnum bit length
 *
 * @return result x^-1 mod P
 *
 */
static ErrStatus ecc_modinv(const uint32_t P[], const uint32_t U[], uint8_t wnum, uint16_t bnum, uint32_t result[], PassFlag *flag)
{
    ErrStatus tmp_err = SUCCESS;
    uint8_t i;
    for( i = 0U; i < wnum; i++)
    {
        SEA_ECC_MAP_CONST->REG[i] = 0U;
        SEA_ECC_MOD_P->REG[i] = P[i];
        SEA_ECC_MOD_PT->REG[i] = P[i];
        SEA_ECC_MAP_U->REG[i] = U[i];
        SEA_ECC_MAP_UT->REG[i] = U[i];
    } 
    SEA_ECC_MAP_CONST->REG[0] = 1U;
    SEA_CYPHER->NLen = bnum;

    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_ModInv, SEA_TIMEOUT_IR_ECC_ModInv, &tmp_err);

    if((SEA_CYPHER->Status & 2U)==0U){
        *flag =  FAIL;
    }else{
        *flag =  PASS;
    }

    for( i = 0U; i < wnum; i++)
    {
        result[i] = SEA_ECC_MAP_UZ->REG[i];
    }

    return tmp_err;
}
/**
 * @brief ecc_add_i2m
 * @param wnum word length
 * @param A curve parameter a
 * @param Q0_X vector Q0 x-coordinate
 * @param Q0_Y vector Q0 y-coordinate
 * @param Q1_X vector Q1 x-coordinate
 * @param Q1_Y vector Q1 y-coordinate
 * 
 */
static ErrStatus ecc_add_i2m(uint8_t wnum, const uint32_t A[],const uint32_t Q0_X[], const uint32_t Q0_Y[], const uint32_t Q1_X[], const uint32_t Q1_Y[])
{
    uint8_t i;
    ErrStatus tmp_err = SUCCESS;
    SEA_ECC_MAP_CONST->REG[0] = 1U;
    SEA_ECC_MAP_Q0_Z->REG[0]  = 1U;
    SEA_ECC_MAP_Q1_Z->REG[0]  = 1U;
    for(i = 1U; i < wnum; i++)
    {
        SEA_ECC_MAP_CONST->REG[i] = 0U;
        SEA_ECC_MAP_Q0_Z->REG[i]  = 0U;
        SEA_ECC_MAP_Q1_Z->REG[i]  = 0U;
    }
    
    for(i = 0U; i < wnum; i++)
    {
        SEA_ECC_MAP_A->REG[i] = A[i];
        SEA_ECC_MAP_Q0_X->REG[i] = Q0_X[i];
        SEA_ECC_MAP_Q1_X->REG[i] = Q1_X[i];
        SEA_ECC_MAP_Q0_Y->REG[i] = Q0_Y[i];
        SEA_ECC_MAP_Q1_Y->REG[i] = Q1_Y[i];
    }
    
    SEA_ECC_IR_FUNC_RUN(SEA_CTRL_IR_ECC_ADD_I2M, SEA_TIMEOUT_IR_ECC_ADD_I2M, &tmp_err);
    
    return tmp_err;
}
/**
 * @brief ecc_infinity
 * @param wnum word length
 * 
 * @return infinity whether the result is infinity or not
 */
static void ecc_infinity(const uint8_t wnum, PassFlag* infinity_status)
{
    const uint32_t const0[17] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};

    (void)ecc_m2i();
    if(ecc_cmp_unsafe(const0,(uint32_t*)(SEA_EMB_BASEADDR + 0x090),wnum)>=0)
    {
        *infinity_status = FAIL;
    }
}


/******************************************************************************
 * Variables used to hold the expanded key, the configuration and a
 * pointer to the private key
 *****************************************************************************/
/**
 * @brief LL_ECC_DHkeygen
 * @param curve:ECC curve's parameter
 * @param[in] pkey:private key from A
 * @param[in] Q:public key including Qx and Qy from B
 *
 * @return DHkey:negotiate key including DHkey_x and DHkey_y
 */
ErrStatus LL_ECC_DHkeygen(const uECC_Curve_t* curve, const uint32_t Q[], const uint32_t pkey[], uint32_t DHkey[]){
    ErrStatus tmp_err = SUCCESS;
    sea_init_all();
    (void)ecc_point_mult(curve->p,curve->a,pkey,Q, &Q[curve->num_words],curve->num_words,curve->num_bits,DHkey, (uint32_t *)&DHkey[curve->num_words]);
    if(CYPHER_OK != checkErrors()){
        tmp_err = ERROR;
    }
    return tmp_err;
 }

/**
 * @brief LL_ECC_keygen
 * @param curve:ECC curve's parameter
 *
 * @param[out] pkey:private key
 * @param[out] Q:public key including Qx and Qy
 * @return ErrStatus  SUCCESS or ERROR
 */
ErrStatus LL_ECC_keygen(const uECC_Curve_t* curve, uint32_t Q[], uint32_t pkey[]){
    ErrStatus tmp_err = SUCCESS;
    uint32_t  random[17];
    uint32_t  i, const0[17];

    for(i=0; i<17U; i++){
        const0[i] = 0;
    }
    clrErrorCode(0xff);
    sea_init_all();
    LL_TRNG_gen(random,curve->num_words);
    (void)ecc_modadd(curve->n, random, const0, curve->num_words, curve->num_bits, pkey);
    (void)ecc_point_mult(curve->p,curve->a,pkey,curve->G, (uint32_t *)(uint32_t)&curve->G[17],curve->num_words,curve->num_bits,Q,(uint32_t *)(uint32_t)&Q[curve->num_words]);
    if(CYPHER_OK !=checkErrors()){
        tmp_err = ERROR;
    }

    return tmp_err;
}

/**
 * @brief LL_ECC_public_keygen
 * @param curve:ECC curve's parameter
 *
 * @param[in] pkey:private key
 * @param[out] Q:public key including Qx and Qy
 * @return ErrStatus  SUCCESS or ERROR
 */
ErrStatus LL_ECC_public_keygen(const uECC_Curve_t* curve, uint32_t Q[], const uint32_t pkey[]){
    ErrStatus tmp_err = SUCCESS;

    clrErrorCode(0xff);
    sea_init_all();
    if(ecc_cmp_unsafe(pkey,curve->p,curve->num_words)==-1)
    {    
        (void)ecc_point_mult(curve->p,curve->a,pkey,curve->G, (uint32_t *)(uint32_t)&curve->G[17],curve->num_words,curve->num_bits,Q,(uint32_t *)(uint32_t)&Q[curve->num_words]);
        if(CYPHER_OK !=checkErrors()){
            tmp_err = ERROR;
        }
    }
    else
    {
        setErrorCode(ECC_PKEY_FAILURE) ;
        tmp_err = ERROR;
    }
    return tmp_err;
}
/**
 * @brief LL_ECC_keyver
 * @param curve:ECC curve's parameter
 * @param[in] Q：public key including Qx and Qy
 * @param[out] ver_flag: PASS-0, FAIL-1
 * @return ErrStatus  SUCCESS or ERROR
 */
ErrStatus LL_ECC_keyver(const uECC_Curve_t* curve, const uint32_t Q[], PassFlag* ver_flag)
{
    ErrStatus tmp_err = SUCCESS;
    clrErrorCode(0xff);
    sea_init_all();
    (void)ecc_point_oncurve(curve->p,curve->a,curve->b, Q, &Q[curve->num_words] , curve->num_words, curve->num_bits,ver_flag);
    /* if(*ver_flag)*/
    /*     return ERROR;*/
    if(CYPHER_OK != checkErrors()){
        tmp_err = ERROR;
    }
    return tmp_err;
}
/**
 * @brief LL_ECC_sign_gen
 * @param curve:ECC curve's parameter

 * @param[in] Hm:message by HASH
 * @param[out] pkey:private key
 * @param[out] Q:public key including Qx and Qy
 * @param[out] (r,s):sign num pair
 * @return ErrStatus  SUCCESS or ERROR
 */
ErrStatus LL_ECC_sign_gen(const uECC_Curve_t* curve, const uint32_t Hm[],uint32_t Q[],uint32_t pkey[], uint32_t r[], uint32_t s[])
{
    PassFlag   tmp_flag  =  PASS;
    ErrStatus  tmp_err = SUCCESS;
    uint32_t i, resultX0[17],resultY0[17];
    uint32_t k[17],k_inv[17];
    uint32_t const0[17];

    for(i=0; i<17U; i++){
        const0[i] = 0;
    }
    clrErrorCode(0xff);
    (void)LL_ECC_keygen(curve,Q,pkey);
    LL_TRNG_gen(k,curve->num_words);
    (void)ecc_modadd(curve->n, k, const0, curve->num_words, curve->num_bits, k);/*k*/

    if(CYPHER_OK == checkErrors()){
        for(i=0U; i<10U; i++){
            /*k^-1*/
            if(SUCCESS != ecc_modinv(curve->n, k, curve->num_words,curve->num_bits,k_inv, &tmp_flag)){
                tmp_err = ERROR;
            }
            else{
                if( PASS != tmp_flag){
                    if(1U == (k[0]&1U)){
                        k[0] -= 2U;
                    }else{
                        k[0] -= 1U;
                    }
                }
            }
            if((SUCCESS != tmp_err ) ||  (PASS == tmp_flag)){
                break;
            }
        }
        if(i >= 10U){
            setErrorCode(ECC_LOOPTIMETOUT_FAILURE) ;
            tmp_err = ERROR;
        }

        if((CYPHER_OK == checkErrors()) && (PASS == tmp_flag)){
            (void)ecc_point_mult(curve->p,curve->a,k,curve->G,(uint32_t*)(uint32_t)&curve->G[17U],curve->num_words,curve->num_bits,resultX0,resultY0);/*k*G*/
            (void)ecc_modadd(curve->n, resultX0, const0, curve->num_words, curve->num_bits, r);/*resultX0 mod N*/
            (void)ecc_modmult(curve->n,pkey,r,curve->num_words,curve->num_bits,resultY0);/*d*r*/
            (void)ecc_modadd(curve->n,Hm,resultY0,curve->num_words,curve->num_bits,resultX0);/*Hm+d*r*/
            (void)ecc_modmult(curve->n,k_inv,resultX0,curve->num_words,curve->num_bits,s);/*k^-1*(Hm+d*r)*/
        }
    }

    if(CYPHER_OK != checkErrors()){
        tmp_err = ERROR;
    }
    return tmp_err;
}
/**
 * @brief LL_ECC_specify_pkey_sign_gen
 * @param curve:ECC curve's parameter

 * @param[in] Hm:message by HASH
 * @param[in] pkey:private key
 * @param[out] Q:public key including Qx and Qy
 * @param[out] (r,s):sign num pair
 * @return ErrStatus  SUCCESS or ERROR
 */
ErrStatus LL_ECC_specify_pkey_sign_gen(const uECC_Curve_t* curve, const uint32_t Hm[],uint32_t Q[], const uint32_t pkey[], uint32_t r[], uint32_t s[])
{
    PassFlag   tmp_flag  =  PASS;
    ErrStatus  tmp_err = SUCCESS;
    uint32_t i, resultX0[17],resultY0[17];
    uint32_t k[17],k_inv[17];
    uint32_t const0[17];

    for(i=0; i<17U; i++){
        const0[i] = 0;
    }
    clrErrorCode(0xff);

    (void)LL_ECC_public_keygen(curve,Q,pkey);
    
    LL_TRNG_gen(k,curve->num_words);
    (void)ecc_modadd(curve->n, k, const0, curve->num_words, curve->num_bits, k);/*k*/

    if(CYPHER_OK == checkErrors()){
        for(i=0U; i<10U; i++){
            /*k^-1*/
            if(SUCCESS != ecc_modinv(curve->n, k, curve->num_words,curve->num_bits,k_inv, &tmp_flag)){
                tmp_err = ERROR;
            }
            else{
                if( PASS != tmp_flag){
                    if(1U == (k[0]&1U)){
                        k[0] -= 2U;
                    }else{
                        k[0] -= 1U;
                    }
                }
            }
            if((SUCCESS != tmp_err ) ||  (PASS == tmp_flag)){
                break;
            }
        }
        if(i >= 10U){
            setErrorCode(ECC_LOOPTIMETOUT_FAILURE) ;
            tmp_err = ERROR;
        }


        if((CYPHER_OK == checkErrors()) && (PASS == tmp_flag)){
            (void)ecc_point_mult(curve->p,curve->a,k,curve->G,(uint32_t*)(uint32_t)&curve->G[17U],curve->num_words,curve->num_bits,resultX0,resultY0);/*k*G*/
            (void)ecc_modadd(curve->n, resultX0, const0, curve->num_words, curve->num_bits, r);/*resultX0 mod N*/
            (void)ecc_modmult(curve->n,pkey,r,curve->num_words,curve->num_bits,resultY0);/*d*r*/
            (void)ecc_modadd(curve->n,Hm,resultY0,curve->num_words,curve->num_bits,resultX0);/*Hm+d*r*/
            (void)ecc_modmult(curve->n,k_inv,resultX0,curve->num_words,curve->num_bits,s);/*k^-1*(Hm+d*r)*/
        }
    }

    if(CYPHER_OK != checkErrors()){
        tmp_err = ERROR;
    }
    return tmp_err;
}
/**
 * @brief LL_ECC_sign_ver
 * @param curve:ECC curve's parameter
 * @param[in] Hm:message by HASH
 * @param[in] Q:public key includeing Qx and Qy
 * @param[in] (r,s):sign num pair
 * @param[out] ver_flag: PASS-0,FAIL-1
 * @return ErrStatus  SUCCESS or ERROR
 */
ErrStatus LL_ECC_sign_ver(const uECC_Curve_t* curve, const uint32_t Hm[], const uint32_t Q[], const uint32_t r[], const uint32_t s[], PassFlag* ver_flag)
{
    PassFlag infinity_status = PASS;
    ErrStatus tmp_err = SUCCESS;
    const uint32_t const0[17] = {0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0};
    uint32_t i, resultX0[17],resultY0[17],resultX1[17],resultY1[17],resultX2[17];
    int tmp_cmp_sta[4];

    clrErrorCode(0xff);

    sea_init_all();
    (void)ecc_point_oncurve(curve->p,curve->a,curve->b, Q, (uint32_t *)(uint32_t)&Q[curve->num_words] , curve->num_words, curve->num_bits, &infinity_status);

    if((CYPHER_OK == checkErrors()) && (PASS == infinity_status) ){
      tmp_cmp_sta[0] = ecc_cmp_unsafe(r,curve->n,curve->num_words) ;
      tmp_cmp_sta[1] = ecc_cmp_unsafe(const0,r,curve->num_words)   ;
      tmp_cmp_sta[2] = ecc_cmp_unsafe(s,curve->n,curve->num_words) ;
      tmp_cmp_sta[3] = ecc_cmp_unsafe(const0,s,curve->num_words)   ;

        if(   (tmp_cmp_sta[0]  >= 0  )
            || (tmp_cmp_sta[1]  >= 0  )
            || (tmp_cmp_sta[2]  >= 0  )
            || (tmp_cmp_sta[3]  >= 0  ) )
        {
            infinity_status = FAIL;
        }
        else
        {
            /*s^-1*/
            if(SUCCESS != ecc_modinv(curve->n, s, curve->num_words,curve->num_bits,resultX0, &infinity_status)){
                tmp_err = ERROR;
            }

            if((CYPHER_OK == checkErrors()) && (PASS == infinity_status)){
                (void)ecc_modmult(curve->n,Hm,resultX0,curve->num_words,curve->num_bits,resultX1);/*u1 = s^-1*Hm*/
                (void)ecc_modmult(curve->n,r,resultX0,curve->num_words,curve->num_bits,resultX2);/*u2 = s^-1*r*/
                (void)ecc_point_mult(curve->p,curve->a,resultX1,curve->G,(uint32_t*)(uint32_t )&curve->G[17],curve->num_words,curve->num_bits,resultX0,resultY0);/*u1*G*/
                (void)ecc_point_mult(curve->p,curve->a,resultX2,Q,(uint32_t*)(uint32_t )&Q[curve->num_words],curve->num_words,curve->num_bits,resultX1,resultY1);/*u2*Q*/
                (void)ecc_add_i2m(curve->num_words,curve->a,resultX0,resultY0,resultX1,resultY1);
                (void)ecc_point_add();
                ecc_infinity((uint8_t)curve->num_words,&infinity_status);

                if((CYPHER_OK == checkErrors()) && (infinity_status == PASS) ){
                    for(i = 0U; i < curve->num_words; i++){
                        resultX2[i] = SEA_ECC_MAP_Q0_X->REG[i];
                    }

                    if(SUCCESS == ecc_modadd(curve->n,const0,resultX2,curve->num_words,curve->num_bits,resultX1)){
                        for(i = 0U; i < curve->num_words; i++){
                            if(resultX1[i] != r[i]){
                                infinity_status = FAIL;
                                break;
                            }
                        }
                    }
                }
            }
        }
    }

    if(PASS != infinity_status){
       *ver_flag = FAIL;
    }else{
       *ver_flag = PASS;
    }

    return tmp_err;
}
#endif
/* r2212 */
