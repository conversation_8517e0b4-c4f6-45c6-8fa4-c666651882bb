/**
 *******************************************************************************
 * @file  template/source/main.c
 * @brief Main program template for the Device Driver Library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-09-08       XT             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/

/**
 * @addtogroup LL_Templates
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/

/**
 * @brief  Main function of template project
 * @param  None
 * @retval int32_t return value, if needed
 */
#include "main.h"
#ifdef LL_EMU_DEMO
#include "rn8xxx_ll_emu_demo.h"

void BSP_GPIO_ForPulse_StructInit(GPIO_InitTypeDef *sGPIO_Init, eGPIOFunction_TypeDef Fun)
{
  sGPIO_Init->Mode = Fun;                /*!< 复用模式 ,此处不能选择_SPI1*/
  sGPIO_Init->OutputLevel = High_Level;  /*"输出电平选择"*/
  sGPIO_Init->Pull = Pull_OFF;           /*!< 上拉选择 */
  sGPIO_Init->Dir = GPIO_MODE_IN;        /*!< 输入输出选择 */
  sGPIO_Init->InputMode = TTL_MODE;      /*!< 输入模式*/
  sGPIO_Init->OutputMode = PushPll_MODE; /*!< 输出模式*/
}

void BSP_GPIO_PxxForPulseInit(uint32_t pxx, GPIO_InitTypeDef *sGPIO_Init)
{
  sGPIO_Init->Pin = pxx; /*!< Specifies the GPIO pins to be configured.This parameter can be any value of @ref GPIO_pins */
  LL_GPIO_Init(sGPIO_Init);
}

#if defined(RN821x_RN721x_SOC_V2)
sAdcWaveData_TypeDef AdcWaveData;
sMeasureRms_TypeDef MeasureRms; // 计量转换为实际值的有效值

/*****************************************************************************
** Function name:	fnMeasure_WaveData
**
** Description:	1、接收RN8209波形缓存，对接收数据进行合法性判断，封装到电压电流缓存
**              2、满一周波数据后进行拉格朗日插值
**              3、将插值后波形数据输出到另一组SPI，供其他模块进行电能质量分析
**              4、保存剩余的波形点数，避免波形不连续
**
** Parameters:Item	: 0: UART DMA半满中断
**                    1: UART DMA全满中断
** Returned value:
**
******************************************************************************/
void fnMeasure_WaveData(uint32_t dmastatus)
{
  uint32_t saddr, daddr;

  uM2MMode_TypeDef M2MModePara;

  sll_M2M_InitTypeDef sMem_Init;

  if (dmastatus & 0x01) //-------DMA接收全满中断标志---------
  {
    //------波形缓存数据分别封装到电压电流波形缓存，形成完整周波数据---
    M2MModePara.M2MMode = 0x00;
    M2MModePara.bitM2MMode.IVLD = IVLD_4BYTE;
    M2MModePara.bitM2MMode.OVLD = OVLD_2BYTE;
    M2MModePara.bitM2MMode.ORV_EN = ORV_DIS; //
    M2MModePara.bitM2MMode.DUMMY_EN = DUMMY_EN;

    //--------------------
    saddr = ((uint32_t)(&AdcWaveData.TempWaveBuf[0][0])); // 数据源地址
    daddr = ((uint32_t)(&AdcWaveData.WaveBuf[0][0][0]));  // 目标地址

    sMem_Init.SAddr = saddr;
    sMem_Init.DAddr = daddr;
    sMem_Init.TxLen = DMA_NUMPOINT * 2;
    sMem_Init.DUMMY = 1;
    LL_M2M_Move(&sMem_Init); // 每个半满，64点数据

    AdcWaveData.StarFlag[0] = CTLSTOP;
  }
}
/* EMU->IE内启动事件对应中断函数 */
void EMU_HANDLER(void)
{
}
/* EMU->IE3内启动事件对应中断函数 */
void DMA_HANDLER(void)
{
  uint32_t emu_status3 = 0, u32emu_buf = 0;

  emu_status3 = LL_EMU_RegRead(LL_EMU_IF3, (uint8_t *)&u32emu_buf, 2);

  fnMeasure_WaveData(emu_status3);

  LL_EMU_RegWrite(LL_EMU_IF3, (uint8_t *)&emu_status3, 1);
}

void EMU_DATA_Output(void)
{
  uint32_t u32emu_buf = 0;
  LL_EMU_RegRead(LL_EMU_IARMS, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_IARMS %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_IBRMS, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_IBRMS %x\n", u32emu_buf);

  LL_WDT_ReloadCounter();
  LL_EMU_RegRead(LL_EMU_URMS, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_URMS %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerPA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PowerPA %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerPB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PowerPB %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerQA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PowerQA %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerQB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PowerQB %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerSA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PowerSA %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerSB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_IBRMS %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PFA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PFA %x\n", u32emu_buf);

  LL_WDT_ReloadCounter();
  LL_EMU_RegRead(LL_EMU_PFB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PFB %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_ANGLEA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_ANGLEA %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_ANGLEB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_ANGLEB %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_SPL_IA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_SPL_IA %x\n", u32emu_buf);

  LL_WDT_ReloadCounter();
  LL_EMU_RegRead(LL_EMU_SPL_IB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_SPL_IB %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_SPL_U, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_SPL_U %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerPA2, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PowerPA2 %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_PowerPB2, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_PowerPB2 %x\n", u32emu_buf);

  LL_WDT_ReloadCounter();
  LL_EMU_RegRead(LL_EMU_SPL_PA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_SPL_PA %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_SPL_PB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_SPL_PB %x\n", u32emu_buf);

  LL_WDT_ReloadCounter();
  LL_EMU_RegRead(LL_EMU_SPL_QA, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_SPL_QA %x\n", u32emu_buf);

  LL_EMU_RegRead(LL_EMU_SPL_QB, (uint8_t *)&u32emu_buf, 3);
  printf("LL_EMU_SPL_QA %x\n", u32emu_buf);
}

void WAVE_DMA_Enable(void)
{
  uint32_t u32emu_buf = 1;
  LL_EMU_RegWrite(LL_EMU_DMA_BUF_CTRL, (uint8_t *)&u32emu_buf, 1);
}
void WAVE_DMA_Config(void)
{
  uint32_t u32emu_buf = 0;

  u32emu_buf = 0xe5;
  LL_EMU_RegWrite(LL_EMU_SPCMD, (uint8_t *)&u32emu_buf, 1);

  //  u32emu_buf = (0<<14)\ /* 不使能间隔存储 */
  //                |(1<<10)\ /* 周波区块数 */
  //                |(0<<9)\ /* 校验和：0：不使能 */
  //                |(0<<8)\ /* DMA单次模式或循环模式：0：单次模式 */
  //                |(0<<7)\ /* DMA存储模式：0：按通道存放数据；1：连续存放 */
  //                |(1<<5)\ /* 使能IA通道 */
  //                |(1<<4)\/* 使能U通道 */
  //                |(0x0<<0);/* DMA波形数据源头选择：00:7.2K;  01:14.4K */
  u32emu_buf = (0 << 14) | (1 << 10) | (0 << 9) | (1 << 8) | (0 << 7) | (1 << 5) | (1 << 4) | (0x0 << 0); /* DMA波形数据源头选择：00:7.2K;  01:14.4K */
  LL_EMU_RegWrite(LL_EMU_DMA_WAVE_CFG, (uint8_t *)&u32emu_buf, 2);

  /* 数据缓存目标地址 -*/
  u32emu_buf = ((uint32_t)&AdcWaveData.TempWaveBuf) / 4 & 0x1fff;
  LL_EMU_RegWrite(LL_EMU_DMA_BUF_BADDR, (uint8_t *)&u32emu_buf, 2);

  /* 数据缓存深度 7.2k 50Hz电网频率下，一周波144点数据-*/
  u32emu_buf = 144;
  LL_EMU_RegWrite(LL_EMU_DMA_BUF_DEPTH, (uint8_t *)&u32emu_buf, 2);

  /* 数据缓存深度 -*/
  u32emu_buf = 0;
  LL_EMU_RegWrite(LL_EMU_DMA_GAP_CFG, (uint8_t *)&u32emu_buf, 3);

  u32emu_buf = 3; /* 波形缓存DMA全满、半满中断 */
  LL_EMU_RegWrite(LL_EMU_IE3, (uint8_t *)&u32emu_buf, 3);

  NVIC_EnableIRQ(DMA_IRQn); /* EMU中断使能 */
}
#endif
void LL_EMU_Exe_Demo(void)
{
  uint32_t u32emu_buf = 0;
  GPIO_InitTypeDef GPIO_Init;
  LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);
  BSP_GPIO_ForPulse_StructInit(&GPIO_Init, _PF);
  BSP_GPIO_PxxForPulseInit(PIN_5_0, &GPIO_Init);

#if defined(RN202x_RN7326_SOC_V2)
  /*初始化完成后，分别用等待发送接收，中断发送接收；DMA发送接收；来测试 */
  LL_SYSC_ApbClkCtrl(LL_SYSC_EMU_ID, ERN_ENABLE);
  LL_SYSC_ApbClkCtrl(LL_SYSC_NVM_ID, ERN_ENABLE);

  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCIA, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCIB, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCIC, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCUA, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCUB, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCUC, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCIN, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_VREFA, ERN_ENABLE);

  u32emu_buf = 0xe5;
  LL_EMU_RegWrite(LL_EMU_SPCMD, (uint8_t *)&u32emu_buf, 1);
  u32emu_buf = 0x1234;
  LL_EMU_RegWrite(LL_EMU_HFConst1, (uint8_t *)&u32emu_buf, 2);
  u32emu_buf = 0x4321;
  LL_EMU_RegWrite(LL_EMU_HFConst2, (uint8_t *)&u32emu_buf, 2);
  u32emu_buf = 0x0;
  LL_EMU_RegWrite(LL_EMU_SPCMD, (uint8_t *)&u32emu_buf, 1);

  LL_EMU_RegRead(LL_EMU_HFConst2, (uint8_t *)&u32emu_buf, 2);
#elif defined(RN821x_RN721x_SOC_V2)
  LL_SYSC_ApbClkCtrl(LL_SYSC_EMU_ID, ERN_ENABLE);
  LL_SYSC_ApbClkCtrl(LL_SYSC_NVM_ID, ERN_ENABLE);

  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCI1, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCI2, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_ADCU, ERN_ENABLE);
  LL_SYSC_AnaPowerCtrl(LL_SYSC_PD_BGR, ERN_ENABLE);
  LL_SYSC_ADCPGACtrl(LL_SYSC_ADCCHN_I1, LL_SYSC_ADCPGA_16);

  u32emu_buf = 0xe5;
  LL_EMU_RegWrite(LL_EMU_SPCMD, (uint8_t *)&u32emu_buf, 1);
  u32emu_buf = 0x11c7;
  LL_EMU_RegWrite(LL_EMU_HFConst, (uint8_t *)&u32emu_buf, 2);
  //  u32emu_buf = 0;
  //  LL_EMU_RegWrite(LL_EMU_SPCMD,(uint8_t *)&u32emu_buf,1);

  LL_EMU_RegRead(LL_EMU_HFConst, (uint8_t *)&u32emu_buf, 2);

  WAVE_DMA_Config();
  WAVE_DMA_Enable();

#endif
}
#endif

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
