/**
 *******************************************************************************
 * @file  template/source/main.h
 * @brief This file contains the including files of main routine.
 @verbatim
   Change Logs:
   Date             Author              Notes
   2023-08-31       Renergy             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Micro-Technologies(Shenzhen)CO., LTD.
 *  All rights reserved.
 *
 *
 *******************************************************************************
 */
#ifndef RN8xxx_LL_EMU_DEMO_H
#define RN8xxx_LL_EMU_DEMO_H

// #ifdef   MEASURE_GLOBALS
// 	#define  MEASURE_EXT
// #else
// 	#define  MEASURE_EXT  extern
// #endif

extern void LL_WDT_Exe_Demo(void);
extern void EMU_DATA_Output(void);
extern void WAVE_DMA_Enable(void);

typedef enum
{
  CTLSTAR = 0x5a,
  CTLSTOP = !CTLSTAR
} FunctionalControl;

#define ThreePhsShunt 1
#define DMA_NUMPOINT 144 //

#define nLagrange 128 // 256			// 插值算法点数
#define NUM_FFT 128   // 128 			// 瞬时数据点数

#define nPhs 1

typedef struct
{
  uint16_t PConstE;              // 脉冲常数
  uint16_t Hfconst;              //
  uint32_t StandUn[nPhs];        // 校正时台体标准电压
  uint32_t StandUnreg[nPhs];     // 校正时台体电压寄存器标准值
  uint32_t StandIb[nPhs + 1];    // 校正时台体标准电流
  uint32_t StandIbreg[nPhs + 1]; // 校正时台体电流寄存器标准值
  uint32_t StandPw[nPhs];        // 校正时台体标准功率
  uint32_t StandPwreg[nPhs];     // 校正时台体功率寄存器标准值
  int32_t Err[nPhs];             // 误差

} sCorrectTmp_TypeDef;

typedef struct
{
  uint16_t WaveCnt;                                     // 当前接收数据点
  uint16_t WaveNUM;                                     // 插值前一周波点数，跟随频率变化而变化
  uint32_t TempWaveBuf[ThreePhsShunt][2][DMA_NUMPOINT]; // UA IA UB IB UC IC存储
  uint32_t WaveBuf[ThreePhsShunt][2][DMA_NUMPOINT];     // UA IA UB IB UC IC存储,从8209D中数据转存，多存39个点，为尾数处理
  // uint32_t	FIRWaveBuf[ThreePhsShunt][2][RN8209_MAXPOINT];	// FIR 波形缓存，一个周波
  // uint32_t	LagWaveBuf[ThreePhsShunt][2][nLagrange*2+10];	// 拉格朗日插值后数据
  float fLagWaveBuf[ThreePhsShunt][2][nLagrange * 2 + 10]; // 拉格朗日插值后数据
  // uint32_t	SyncWaveBuf[ThreePhsShunt][2][nLagrange*2];		// 同步采样波形，256点
  // uint32_t HarmonicWaveBuf[ThreePhsShunt][2][NUM_FFT];		// 谐波计算波形数据
  // uint32_t HWWaveBuf[ThreePhsShunt][2][64];					// 用于存储半周波数据，计算半波有效值
  // uint32_t HW_RMS[ThreePhsShunt][2][4];						// 半波有效值，根据2周波波形数据得到4个半周波数据
  // uint32_t LastHW_RMS[ThreePhsShunt][2];					// 前半周波平方和累加数据
  // uint32_t WaveFirPara[11];						// 10阶滤波器参数
  // sSpiOutData_Type	TxOut;					// 发送数据
  // uint16_t LagOffset[2];
  // uint32_t LagOutLen[2];
  // uint32_t Lag_t[2];
  // uint8_t	nWaveFlag;								// 数据点数标志，0x5a： 波形数据256点，谐波数据为256中抽取128点  其它：波形数据为128点
  // uint8_t	nHarDataCnt;							// 单相表只要21次谐波，每周波取64点，128点做谐波分析
  uint32_t LsatData_U;           // Lagrange 上周期的最后一个数据
  uint32_t LsatData_I;           // Lagrange 上周期的最后一个数据
  FunctionalControl StarFlag[3]; // 谐波计算标志
  // FunctionalControl SendFlag;					// 波形数据发送标志
  uint8_t StartCnt;

  // sHalfIrms_TypeDef   HalfIrms;   			//半波有效值

} sAdcWaveData_TypeDef;

typedef struct
{
  struct sMeasureRmsPubData_TypeDef
  {
    uint32_t U[3]; //---电压---NNN.NNN v  三位小数
    int32_t I[3];  //---电流NNNN.NNNN(4位小数，最高位表示方向)---16
    int32_t In;    //--零线-电流NNNN.NNNN(4位小数，最高位表示方向)---16

    int32_t Pw[4]; //{Pa Pb  } 有功功率 ,单位W，3位小数
    int32_t Pq[4]; //{Qa Qb   } 无功功率 ,单位W，3位小数
    int32_t Ps[4]; //{Sa Sb   } 视在功率 ,单位W，3位小数

    int16_t Pf[2];                //---功率因数N.NNNN---	最高位表示方向{Pf Pfa Pfb Pfc}	8  sDF05
    uint16_t Angle[nPhs * 3 + 1]; //---相角NNN.N---		18  /*"UBA UCA IAUA IBUB ICUC"*/
                                  // PhUa,PhUb,PhUc, 以A相电压为基准，B、C相角度				//(hyg) BCD码
                                  // PhIa,PhIb,PhIc,A相电流与A相电压间角度、B相电流与B相电压间角度、C相电流与C相电压间角度
                                  // Angle A,Angle B,Angle C, A相电流与A相电压间角度、B相电流与A相电压间角度、C相电流与A相电压间角度
                                  // Angle C-Angle A ,C相电流与A相电流间角度

    uint16_t Frequency;       // NN.NN 2
    int32_t PPwave[nPhs + 1]; // NN.NNN 3 �d单位:W  ,1分钟平均有功功率
    int32_t Pqwave[nPhs + 1]; // 分钟平均无功功率3位小数存储
    int32_t Pswave[nPhs + 1]; // 分钟平均视在功率

    uint32_t HUrms[21]; // 基波及谐波电压,V,4位小数
    int32_t HIrms[21];  // 基波及谐波电流,A,4位小数
    int32_t HPrms[21];  // 基波及谐波功率,w,4位小数

  } PubData;

  struct sMeasureRmsPriData_TypeDef
  {
    uint8_t Flag; //---工作异常标志---
    uint16_t MeasureStamp;
    uint16_t Measure_IFStamp;        // 中断超时
    uint16_t Measure_IFStampPhsX[3]; // 中断超时
    uint8_t gFUrmValid;
  } PriData;

  struct sMeasureRmsPriPara_TypeDef
  {
    uint32_t PConstE; // 有功常数
    uint16_t Crc;
  } PriPara;
} sMeasureRms_TypeDef;

extern sMeasureRms_TypeDef MeasureRms;   // 计量转换为实际值的有效值
extern sAdcWaveData_TypeDef AdcWaveData; // __attribute__((at(0x10001a70)));	   	//波形缓存计算及状态数据

#endif

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
