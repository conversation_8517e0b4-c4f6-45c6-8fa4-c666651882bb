<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060422::V5.06 update 4 (build 422)::ARMCC</pCCUsed>
      <TargetOption>
        <TargetCommonOption>
          <Device>RN2026_SOC_V2</Device>
          <Vendor>Renergy</Vendor>
          <PackID>Renergy.RN_CM0_Devices.1.1.0</PackID>
          <Cpu>IRAM(0x10000000,0x17000) IROM(0x00000000,0x80000) CPUTYPE("Cortex-M0") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC4000 -FN1 -FF0RN_CM0_Devices -FS00 -FL080000 -FP0($$Device:RN2026_SOC_V2$Flash\RN_CM0_Devices.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:RN2026_SOC_V2$Device\rn202x_rn7326_soc\include\rn202x_rn7326_soc.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:RN2026_SOC_V2$SVD\RN202X_RN7326_SOC_V2.svd</SFDFile>
          <bCustSvd>1</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>RN8xxx_template</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>1</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name>fromelf.exe --bin -o "$<EMAIL>" "#L"</UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments>  </SimDllArguments>
          <SimDlgDll>DARMCM1.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM0</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments> </TargetDllArguments>
          <TargetDlgDll>TARMCM1.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM0</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4099</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3></Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M0"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x17000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x10000000</StartAddress>
                <Size>0x17000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>RN202x_RN7326_SOC_V2</Define>
              <Undefine></Undefine>
              <IncludePath>..\..\..\drivers\cmsis\cm0\CoreSupport;..\..\..\drivers\rn8xxx_ll\inc;..\..\..\drivers\rn8xxx_ll\lib;..\..\..\drivers\bsp\components\RN8209;..\..\..\drivers\rn8xxx_ll\sea;..\..\..\drivers\cmsis\cm0\DeviceSupport\rn202x_rn7326_soc\inc;..\src;..\RTT</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x00000000</TextAddressRange>
            <DataAddressRange>0x10000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile>.\Objects\RN8xxx_template.sct</ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>App</GroupName>
          <Files>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\main.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Bsp</GroupName>
        </Group>
        <Group>
          <GroupName>CoreSupport</GroupName>
          <Files>
            <File>
              <FileName>system_rn202x_rn7326_soc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\cmsis\cm0\DeviceSupport\rn202x_rn7326_soc\src\system_rn202x_rn7326_soc.c</FilePath>
            </File>
            <File>
              <FileName>startup_rn202x_rn7326_soc.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\..\..\drivers\cmsis\cm0\DeviceSupport\rn202x_rn7326_soc\src\mdk\startup_rn202x_rn7326_soc.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Devices</GroupName>
          <Files>
            <File>
              <FileName>rn8xxx_ll_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_common.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_dsp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_dsp.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_emu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_emu.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_gpio.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_iic.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_iic.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_intc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_intc.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_iso7816.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_iso7816.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_kbi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_kbi.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_lcd.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_m2m.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_m2m.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_madc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_madc.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_simptc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_simptc.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_spi.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sysc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sysc.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_systickcortexm0.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_systickcortexm0.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_tc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_tc.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_uart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_uart.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_wdt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_wdt.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_rtc.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Rnlib</GroupName>
          <Files>
            <File>
              <FileName>rn8xxx_ll_clktrim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_clktrim.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_eeprom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_eeprom.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_flash.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_gpadc_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_gpadc_lib.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sipeeprom.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sipeeprom.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sysclk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sysclk.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sysoption.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sysoption.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_utils.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_rtc_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_rtc_lib.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_emu_lib.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_emu_lib.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Sea</GroupName>
          <Files>
            <File>
              <FileName>rn8xxx_ll_sea_aes.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sea_aes.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sea_common.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sea_common.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sea_ecc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sea_ecc.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sea_hash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sea_hash.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sea_trng.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\..\..\drivers\rn8xxx_ll\src\rn8xxx_ll_sea_trng.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Demo</GroupName>
          <Files>
            <File>
              <FileName>rn8xxx_ll_intc_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_intc_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_kbi_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_kbi_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_madc_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_madc_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_simptc_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_simptc_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_spi_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_spi_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_sysc_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_sysc_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_tc_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_tc_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_uart_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_uart_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_wdt_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_wdt_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_gpio_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_gpio_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_lowpower_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_lowpower_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_m2m_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_m2m_demo.c</FilePath>
            </File>
            <File>
              <FileName>rn8xxx_ll_rtc_demo.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\src\rn8xxx_ll_rtc_demo.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>RTT</GroupName>
          <Files>
            <File>
              <FileName>SEGGER_RTT.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RTT\SEGGER_RTT.c</FilePath>
            </File>
            <File>
              <FileName>SEGGER_RTT_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\RTT\SEGGER_RTT_printf.c</FilePath>
            </File>
          </Files>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components/>
    <files/>
  </RTE>

</Project>
