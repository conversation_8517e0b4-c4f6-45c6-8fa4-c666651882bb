#include "main.h"
#ifdef LL_KBI_DEMO
#include "rn8xxx_ll_kbi_demo.h"

void fnKbiTest_Init(void)
{
    GPIO_InitTypeDef gpio_init;

    gpio_init.Pin = PIN_11_0;
    gpio_init.Dir = GPIO_MODE_OUT;
    gpio_init.InputMode = COMS_MODE;
    gpio_init.Mode = _NORMALIO;
    gpio_init.OutputLevel = High_Level;
    gpio_init.OutputMode = PushPll_MODE;
    gpio_init.Pull = Pull_OFF;

    LL_GPIO_Init(&gpio_init);
    
    // LED1
    gpio_init.Pin = PIN_3_3;
    gpio_init.Dir = GPIO_MODE_OUT;
    gpio_init.InputMode = COMS_MODE;
    gpio_init.Mode = _NORMALIO;
    gpio_init.OutputLevel = Low_Level;
    gpio_init.OutputMode = PushPll_MODE;
    gpio_init.Pull = Pull_OFF;

    LL_GPIO_Init(&gpio_init);


    LL_KBI_Disable(KBI_ID3);

    // KBIx
    gpio_init.Pin = PIN_0_1;
    gpio_init.Dir = GPIO_MODE_IN;
    gpio_init.InputMode = COMS_MODE;
    gpio_init.Mode = _KEY;
    gpio_init.OutputLevel = Low_Level;
    gpio_init.OutputMode = PushPll_MODE;
    gpio_init.Pull = Pull_OFF;

    LL_GPIO_Init(&gpio_init);

    LL_KBI_Init(KBI_ID3, KBI_RISIONGEDGE, KBI_IRQ_DISABLE);
}

void LL_KBI_Exe_Demo(void)
{
    if(GET_BIT_KBI_DATA(KBI, BIT3))
    {
        CLR_BIT_KBI_DATA(KBI, BIT3);
        LL_GPIO_SetPin(PIN_3_3, 1);
    }
}

#endif




