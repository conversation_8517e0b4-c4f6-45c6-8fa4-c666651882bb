﻿================================================================================
                              使用说明
================================================================================
Date            Author           IAR       MDK      
2024-11-5      Renergy         7.40      5.23      

================================================================================
功能描述
========
一：本文件夹下提供了IAR和MDK两个平台下，锐能微各系列产品型号的DEMO工程，分别在IAR和MDK文件夹下。

锐能微产品分以下三个大系列：
1、MCU系列：rn831x_rn861x_mcu，该大系列下包含以下几个子系列：RN831x_RN861x_MCU_V2，RN831x_RN861x_MCU_V3
2、单相SOC系列：rn821x_rn721x_soc，该大系列下包含以下几个子系列：RN821x_RN721x_SOC_D、RN821x_RN721x_SOC_V2、RN821x_RN721x_SOC_V3
3、三相SOC系列：rn202x_rn7326_soc，该大系列下包含以下几个子系列：RN202x_RN7326_SOC_V2

二：本文件夹下还提供了低功耗的可执行文件在output_exe文件夹内

================================================================================
DEMO工程使用步骤
========
1）打开IAR或MDK文件夹下对应工程文件：如rn821x_rn721x_soc或多工程文件（与资料包不对应的工程因缺少相应支持文件会出现编译错误）
2）如第一步打开的是多工程文件，则选择所需产品大系列号工程，如rn821x_rn721x_soc；否则跳过该步骤
3）工程配置内，预定义对应的子系列号，如RN821x_RN721x_SOC_V3
4）在main.h内开启相应的宏调试相应DEMO，如#define LL_LOW_POWER_DEMO
5）编译工程
6）启动IDE的下载和调试功能，全速运行；

低功耗可执行文件使用步骤
================
将对应低功耗可执行文件用编程器或JFLASH等下载到芯片内即可

================================================================================
应用注意
========
1、DEMO工程用于锐能微芯片各模块及其ll驱动的应用参考,默认为低功耗DEMO
2、特定资料包下，选择其他产品大系列号工程，会出现编译错误，原因是资料包下缺少相应的支持文件；可下载相应芯片对应系列号的资料包，或将芯片相应系列号资料包内对应系列号支持文件拷贝的相应目录下
================================================================================

