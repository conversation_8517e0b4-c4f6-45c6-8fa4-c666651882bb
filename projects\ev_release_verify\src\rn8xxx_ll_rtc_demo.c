#include "main.h"
#ifdef LL_RTC_DEMO
#include "rn8xxx_ll_rtc_demo.h"

#define RTC_LED PIN_6_0
void BSP_GPIO_PxxForRTCLedInit(uint32_t pxx)
{
  GPIO_InitTypeDef GPIO_Init;
  GPIO_Init.Pin = pxx;                 /*!< Specifies the GPIO pins to be configured.
                                This parameter can be any value of @ref GPIO_pins */
  GPIO_Init.Mode = _NORMALIO;              /*!< 复用模式 */
  GPIO_Init.OutputLevel = Low_Level;   /*"输出电平选择"*/
  GPIO_Init.Pull = Pull_OFF;           /*!< 上拉选择 */
  GPIO_Init.Dir = GPIO_MODE_OUT;       /*!< 输入输出选择 */
  GPIO_Init.InputMode = TTL_MODE;      /*!< 输入模式*/
  GPIO_Init.OutputMode = PushPll_MODE; /*!< 输出模式*/

  LL_GPIO_Init(&GPIO_Init);
}
void RTC_HANDLER(void)
{
    if(GET_BIT_RTC_IF(RTC, 0x02) == 1)
    {
        CLR_BIT_RTC_IF(RTC, 0x02);
        LL_GPIO_OverturnPin(RTC_LED);
    }
    if(GET_BIT_RTC_IF(RTC, 0x04) == 1)
    {
        CLR_BIT_RTC_IF(RTC, 0x03);
        LL_GPIO_OverturnPin(RTC_LED);
    }
  
}
void LL_RTC_Exe_Demo(void)
{
    int32_t temp;
    uint8_t time_buf[7] = {59, 59, 23,6,27,12,24};
    uint8_t i = 0;

    LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);
    BSP_GPIO_PxxForRTCLedInit(RTC_LED);

    LL_RTC_CtlInit(RTC_TSE_AutoMode,RTC_TCP_2Sec, RTC_FOUT_1HZ);
    // LL_RTC_Cnt1Init(1);
    LL_RTC_Cnt2Init(6);
    NVIC_EnableIRQ(RTC_IRQn);
    temp = LL_RTC_TempGet(); 
    LL_RTC_Write(time_buf, 3);  
    WDT->EN = 0XBB;
    LL_RTC_Read(time_buf);    
}
#endif
