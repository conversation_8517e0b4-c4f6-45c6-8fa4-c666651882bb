/**
 *******************************************************************************
 * @file  madc_demo.c
 * @brief
@verbatim
  Change Logs:
  Date             Author          Notes
  2024-11-20       XT             1 Add explain; 2 modify SAR demo
@endverbatim
锐能微芯片模拟测试用例，应用注意点如下；
1、不同系列产品，APB时钟名称有差异，故不同系列产品分开给出demo，用系列名宏定义区分；
2、模拟模块根据应用方式的差别可分为：LVD CMP SAR，
    LVD用于检测芯片供电电压或外部管脚电压与指定电压（可配置）的比较，输出高于或低于两个状态
    CMP用于检测芯片某管脚电压与1.25V的比较结果，输出高于或低于两个状态
    SAR用于芯片某管脚电压值测试
    具体应用，用宏区分

使用方法
1、工程内选择正确的产品系列
2、本文件内定义相应子模块宏定义：LVD_DEMO CMP1_DEMO SAR_DEMO

如需使用串口打印，可打开宏定义LL_UART_PRING，并在main.h中开启UART_DEMO，且保证uart_demo.c中也开启宏定义LL_UART_PRING

 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Semiconductor Co., Ltd. All rights reserved.
 *
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "main.h"
#ifdef LL_MADC_DEMO
#include "rn8xxx_ll_madc_demo.h"
#include "rn8xxx_ll.h"

#define LL_UART_PRING

// #define LVD_DEMO
#define CMP1_DEMO
// #define SAR_DEMO


#if defined(RN831x_RN861x_MCU_V2) || defined(RN831x_RN861x_MCU_V3)
#if defined(LVD_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_CMPLVD_ID
#define BSP_SYSC_PD_MADC LL_SYSC_PD_LVD
#define BSP_LVDCMP_CH LVD_CH
#endif

#if defined(CMP1_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_CMPLVD_ID
#define BSP_SYSC_PD_MADC LL_SYSC_PD_CMP1
#define BSP_LVDCMP_CH CMP1_CH
#define BSP_CMP1_PIN PIN_0_2
#endif

#if defined(SAR_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_RTCSAR_ID
#define BSP_SYSC_PD_MADC LL_SYSC_PD_CMP1

// #define BSP_SAR_CH AIN0_CHANNEL
// #define BSP_SAR_PIN PIN_0_0

// #define BSP_SAR_CH AIN1_CHANNEL
// #define BSP_SAR_PIN PIN_0_1

// #define BSP_SAR_CH AIN3_CHANNEL
// #define BSP_SAR_PIN PIN_0_3
// #define SAR_DEMO_P03

// #define BSP_SAR_CH AIN2_CHANNEL
// #define BSP_SAR_PIN PIN_0_2
// #define SAR_DEMO_P02

// #define BSP_SAR_CH AIN4_CHANNEL
// #define BSP_SAR_PIN PIN_0_4

// #define BSP_SAR_CH AIN5_CHANNEL
// #define BSP_SAR_PIN PIN_4_4

#define BSP_SAR_CH AIN6_CHANNEL
#define BSP_SAR_PIN PIN_4_5
#endif


#elif defined(RN821x_RN721x_SOC_V2) || defined(RN821x_RN721x_SOC_V3)

#if defined(LVD_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_SAR_ID
#define BSP_SYSC_PD_MADC LL_SYSC_PD_LVD
#define BSP_LVDCMP_CH LVD_CH
#endif

#if defined(CMP1_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_SAR_ID
#define BSP_SYSC_PD_MADC LL_SYSC_PD_CMP1
#define BSP_LVDCMP_CH CMP1_CH
#define BSP_CMP1_PIN PIN_0_2
#endif

#if defined(SAR_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_SAR_ID
#define BSP_SYSC_PD_MADC LL_SYSC_PD_BGR

#define BSP_SAR_CH AIN0_CHANNEL
#define BSP_SAR_PIN PIN_0_0

// #define BSP_SAR_CH AIN1_CHANNEL
// #define BSP_SAR_PIN PIN_0_1

// #define BSP_SAR_CH AIN2_CHANNEL
// #define BSP_SAR_PIN PIN_0_2
// #define SAR_DEMO_P02

// #define BSP_SAR_CH AIN3_CHANNEL
// #define BSP_SAR_PIN PIN_0_3
// #define SAR_DEMO_P03


// #define BSP_SAR_CH AIN4_CHANNEL
// #define BSP_SAR_PIN PIN_0_4

// #define BSP_SAR_CH AIN5_CHANNEL /* RN821x_RN721x_SOC_V3 don't support */
// #define BSP_SAR_PIN PIN_1_7

// #define BSP_SAR_CH AIN6_CHANNEL /* RN821x_RN721x_SOC_V3 don't support */
// #define BSP_SAR_PIN PIN_11_6
#endif

#elif defined(RN202x_RN7326_SOC_V2)

#if defined(LVD_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_CMPLVD_ID
#define BSP_SYSC_PD_MADC LL_SYSC_PD_LVD
#define BSP_LVDCMP_CH LVD_CH
#endif
#if defined(CMP1_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_VBAT_ID
#define BSP_SYSC_PD_MADC LL_SYSC_CMPLVD_ID
#define BSP_LVDCMP_CH CMP1_CH
#define BSP_CMP1_PIN PIN_0_0
#endif

#if defined(SAR_DEMO)
#define BSP_SYSC_MADC_ID LL_SYSC_VBAT_ID
#define BSP_SYSC_PD_MADC LL_SYSC_CMPLVD_ID

// #define BSP_SAR_CH AIN0_CHANNEL
// #define BSP_SAR_PIN PIN_4_3

// #define BSP_SAR_CH AIN1_CHANNEL
// #define BSP_SAR_PIN PIN_4_2

// #define BSP_SAR_CH AIN2_CHANNEL
// #define BSP_SAR_PIN PIN_5_1

// #define BSP_SAR_CH AIN3_CHANNEL
// #define BSP_SAR_PIN PIN_5_0

// #define BSP_SAR_CH AIN4_CHANNEL
// #define BSP_SAR_PIN PIN_5_2

// #define BSP_SAR_CH AIN5_CHANNEL
// #define BSP_SAR_PIN PIN_4_1

// #define BSP_SAR_CH AIN6_CHANNEL
// #define BSP_SAR_PIN PIN_4_0

#define BSP_SAR_CH AIN7_CHANNEL
#define BSP_SAR_PIN PIN_5_3

#endif

#endif

void BSP_GPIOSet_SAR(uint8_t u8Pin)
{
  GPIO_InitTypeDef GPIO_Init;

  LL_SYSC_ApbClkCtrl(LL_SYSC_GPIO_ID, ERN_ENABLE);

  GPIO_Init.Mode = _AIN;               /*!< 复用模式 */
  GPIO_Init.OutputLevel = High_Level;  /*"输出电平选择"*/
  GPIO_Init.Pull = Pull_OFF;           /*!< 上拉选择 */
  GPIO_Init.Dir = GPIO_MODE_IN;        /*!< 输入输出选择 */
  GPIO_Init.InputMode = TTL_MODE;      /*!< 输入模式*/
  GPIO_Init.OutputMode = PushPll_MODE; /*!< 输出模式*/

  GPIO_Init.Pin = u8Pin; /*!< Specifies the GPIO pins to be configured.This parameter can be any value of @ref GPIO_pins */
  LL_GPIO_Init(&GPIO_Init);
}
void LL_MADC_Exe_Demo(void)
{
  ErrorStatus power_on = ERN_ERROR, power_off = ERN_ERROR, power_status = ERN_ERROR;
  uint32_t u32adc_data = 0;
  float fadc_value = 0;

  LL_SYSC_ApbClkCtrl(BSP_SYSC_MADC_ID, ERN_ENABLE);   /* 开启APB时钟 */
  LL_SYSC_AnaPowerCtrl(BSP_SYSC_PD_MADC, ERN_ENABLE); /* 开启模拟电源 */  
#if defined(LVD_DEMO) || defined(CMP1_DEMO)
#if defined(LVD_DEMO)
  LL_ANA_LVDInit(LVDS_VOL2P9, LVDCMPSAR_IE_DISABLE); /* LVD初始化 */
#endif
#if defined(CMP1_DEMO)
  BSP_GPIOSet_SAR(BSP_CMP1_PIN);
  LL_ANA_CMPInit(CMP1_CHANNEL, CMP_R600K_ENABLE, CMP_HYSEN_ENABLE, LVDCMPSAR_IE_DISABLE);  
#endif
SystemDelayUs(1000);
  power_status = LL_ANA_LvdCmpStatCheck(BSP_LVDCMP_CH, 1000);   /* 状态获取 */
  power_on = LL_ANA_LvdCmp_STATPowerUp(BSP_LVDCMP_CH, 1000);    /* 状态获取 */
  power_off = LL_ANA_LvdCmp_STATPowerDown(BSP_LVDCMP_CH, 1000); /* 状态获取 */
#ifdef LL_UART_PRING
    printf("\n Power status is %x; Power on is %x; Power off is %x", power_status, power_on, power_off);
#endif
#endif
#if defined(SAR_DEMO)
BSP_GPIOSet_SAR(BSP_SAR_PIN);
#ifdef SAR_DEMO_P03 /* P03管脚具有CMP2功能，作为SAR使用时，CMP2的内部对地600k电阻默认有效，需做关闭处理 */
  LL_ANA_CMPInit(CMP2_CHANNEL, CMP_R600K_ENABLE, CMP_HYSEN_DISABLE, LVDCMPSAR_IE_DISABLE);
#endif
#ifdef SAR_DEMO_P02 /* P02管脚具有CMP1功能，作为SAR使用时，CMP1的内部对地600k需确保关闭；默认对地电阻关闭，如无开启操作，可不做关闭操作 */
  LL_ANA_CMPInit(CMP1_CHANNEL, CMP_R600K_ENABLE, CMP_HYSEN_DISABLE, LVDCMPSAR_IE_DISABLE);
#endif  
  LL_ANA_SarADCInit(BSP_SAR_CH, PGA_0P5); /* SAR AIN0_CHANNEL SAR通道增益选择：PGA_0P5 */
  SystemDelayUs(1000);
  u32adc_data = LL_ANA_SarADCGet();
  fadc_value = u32adc_data / 1024.0 * 1.25 * 2; /* adc寄存器值转换成实际被测电压值 */
  if (fadc_value < 0.01)
  { /* 电压值小于0.01V时，做清零处理 */
    fadc_value = 0;
  }
#ifdef LL_UART_PRING
  printf("\n Voltage is %f at Sar_In; ", fadc_value);
#endif
#endif

 LL_ANA_LVDDisable();
 LL_ANA_CMPDisable(CMP1_CHANNEL);

#if defined(RN831x_RN861x_MCU_V2) || \
    defined(RN831x_RN861x_MCU_V3) || \
    defined(RN202x_RN7326_SOC_V2)
  LL_ANA_LvdCmpSarModeConfig(SAR_NORMALMODE);
#endif

/* 用于位操作测试，实际应用不要使用，会改变寄存器，导致实际应用调用配置函数运行结果出错 */
#if(0)
  GET_REG_ANA_AD_CTRL(MADC, 0x01);
  GET_REG_ANA_AD_START(MADC, 0x01);
  GET_REG_ANA_AD_STAT(MADC, 0x01);
  GET_REG_ANA_AD_DATA(MADC, 0x01);
  GET_REG_ANA_LVD_CTRL(MADC, 0x01);
  GET_REG_ANA_LVD_STAT(MADC, 0x01);

  SET_REG_ANA_AD_CTRL(MADC, 0x01);
  SET_REG_ANA_AD_START(MADC, 0x01);
  SET_REG_ANA_AD_STAT(MADC, 0x01);
  SET_REG_ANA_AD_DATA(MADC, 0x01);
  SET_REG_ANA_LVD_CTRL(MADC, 0x01);
  SET_REG_ANA_LVD_STAT(MADC, 0x01);

  GET_BIT_ANA_AD_CTRL(MADC, 0x01);
  GET_BIT_ANA_AD_START(MADC, 0x01);
  GET_BIT_ANA_AD_STAT(MADC, 0x01);
  GET_BIT_ANA_AD_DATA(MADC, 0x01);
  GET_BIT_ANA_LVD_CTRL(MADC, 0x01);
  GET_BIT_ANA_LVD_STAT(MADC, 0x01);

  SET_BIT_ANA_AD_CTRL(MADC, 0x01);
  SET_BIT_ANA_AD_START(MADC, 0x01);
  SET_BIT_ANA_AD_STAT(MADC, 0x01);
  SET_BIT_ANA_LVD_CTRL(MADC, 0x01);
  SET_BIT_ANA_LVD_STAT(MADC, 0x01);

  CLR_BIT_ANA_AD_CTRL(MADC, 0x01);
  CLR_BIT_ANA_AD_START(MADC, 0x01);
  CLR_BIT_ANA_AD_STAT(MADC, 0x01);
  CLR_BIT_ANA_LVD_CTRL(MADC, 0x01);
  CLR_BIT_ANA_LVD_STAT(MADC, 0x01);
#if defined(RN821x_RN721x_SOC_V2) || \
    defined(RN821x_RN721x_SOC_V3) || \
    defined(RN831x_RN861x_MCU_V2) || \
    defined(RN831x_RN861x_MCU_V3) || \
    defined(RN202x_RN7326_SOC_V2)
  GET_REG_ANA_SAR_CTRL1(MADC, 0x01);
  GET_REG_ANA_SAR_DATA2(MADC, 0x01);

  SET_REG_ANA_SAR_DATA2(MADC, 0x01);

  GET_BIT_ANA_SAR_CTRL1(MADC, 0x01);
  GET_BIT_ANA_SAR_DATA2(MADC, 0x01);

  SET_BIT_ANA_SAR_CTRL1(MADC, 0x01);

  CLR_BIT_ANA_SAR_CTRL1(MADC, 0x01);
#endif

#if defined(RN831x_RN861x_MCU_V2) || defined(RN831x_RN861x_MCU_V3)
  GET_REG_ANA_ANA_PAD(MADC, 0x01);
  GET_REG_ANA_ANA_RST(MADC, 0x01);

  SET_REG_ANA_ANA_PAD(MADC, 0x01);
  SET_REG_ANA_ANA_RST(MADC, 0x01);

  GET_BIT_ANA_ANA_PAD(MADC, 0x01);
  GET_BIT_ANA_ANA_RST(MADC, 0x01);

  SET_BIT_ANA_ANA_PAD(MADC, 0x01);

  SET_BIT_ANA_ANA_RST(MADC, 0x01);
  CLR_BIT_ANA_ANA_PAD(MADC, 0x01);
#endif
#endif
}

#endif
