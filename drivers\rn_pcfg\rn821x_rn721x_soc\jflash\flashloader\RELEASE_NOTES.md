# Change Log

## v1.0.3 (2024-8-18)

### 改动点

#### 功能优化
- 无

#### 功能增加
- 无

#### 修改bug
- 修复部分Jflash版本编程算法报错的bug
- 修复起始地址非0时，编程算法可能报错的bug

## v1.0.2 (2024-7-6)

### 改动点

#### 功能优化
- 无
#### 功能增加
- 无

#### 修改bug
- 修复三相SOC V2版运行于PLL64/2频点后编程算法报错的bug

## v1.0.1 (2024-5-14)

### 改动点

#### 功能优化

#### 功能增加
- 支持MCU V3版系列芯片
- 支持单相SOC V2版、单相SOC V3版系列芯片
- 支持三相SOC V2版系列芯片

#### 修改bug
- 修复客户可能关闭内部超时函数专用定时器时钟导致超时函数失效的bug
- 修复V2芯片RC29M模式flash时序没正确配置的bug

## v1.0.0 (2024-04-20)

### 新建
- 支持MCU V2版系列芯片
