更新记录
------------------------------------------------------------------------------------------------------------------------------------------
V1.3.8  2025-3-17更新记录
1、库函数升级到v1.2.5
2、加密库升级到v1.1.1，修复TRAN使用结束后未关闭的bug
3、LL驱动修改
   config.h文件中三相SOC V2和单相SOC V3两个系列增加对nvm模块的支持
   dsp模块开放LL_DSP_VectorCalc3P4函数支持
   emu模块修订笔误RN821x_RN721X_SOC_D中的笔误为RN821x_RN721x_SOC_D  一个X从大写改成小写x
   gpio模块修改设置：1）开漏输出模式配置函数中：宏定义的笔误(N_OPENDRAIN_OUT修改为N_ch_MODE)   2）           开漏输出模式配置函数中：group_no<9在else if逻辑下，不重新起为else  3） 复用功能配置函数LL_GPIO_CfgFun，修改PIN_5_6配置条件，不再限定为RTC_OUT
   iic模块数据接收函数LL_IIC_HardMode_ReceiveByte，增加状态清零延时，解决跟非EEPROM从机的通讯问题
   madc模块LL_ANA_SarADCGet函数中修改超时函数用的变量为uint32_t，防止死循环
   rtc模块：1）不使用地址定义，全部用头文件定义的寄存器；2）增加对内置库函数的支持 3）温度换算根据不同版本芯片做区分处理

------------------------------------------------------------------------------------------------------------------------------------------
1、SOC V3支持LL_SYSC_ADCPGACtrl
2、四次曲线相关内容只在12寸晶圆产品上得到支持，对应函数有LL_RTC_CurveModeCheck LL_RTC_Mode0TempUpdate
3、全失压文件内的宏定义名称笔误修订LL_MADC_MODULE_ENABLED改为LL_NVM_MODULE_ENABLED
----------------------------------------------------------------------------------------------------------------------------
V1.3.7  2025-1-7更新记录
------------------------------------------------------------------------------------------------------------------------------------------
1、SOC V3支持LL_SYSC_ADCPGACtrl
2、四次曲线相关内容只在12寸晶圆产品上得到支持，对应函数有LL_RTC_CurveModeCheck LL_RTC_Mode0TempUpdate
3、全失压文件内的宏定义名称笔误修订LL_MADC_MODULE_ENABLED改为LL_NVM_MODULE_ENABLED
------------------------------------------------------------------------------------------------------------------------------------------
V1.3.6  2024-12-21更新记录
------------------------------------------------------------------------------------------------------------------------------------------
1、INTC驱动统一不同系列产品接口
2、ISO7816 bug修订，发送启动时，不关闭接收
3、GPIO修订RN202x系列产品P50-P53,sar功能配置异常；修订合封管脚上下拉反置配置
4、MADC修订RN202x系列产品CHANEL7配置bug
5、通用库升级到V1.2.4
6、加密库升级到V1.1.0(新增RSA库函数接口)

------------------------------------------------------------------------------------------------------------------------------------------
V1.3.5  2024-11-07
更新内容：
1、EMU模块支持SOC V3芯片；
2、RTC增加手动测温函数接口；
3、DSP模块增加对三相SOC芯片的支持；
4、通用库函数版本升级到V1.2.3

------------------------------------------------------------------------------------------------------------------------------------------
V1.3.4  2024-10-09
更新内容：

文件数量增加：
1、rtc文件增加，不再与PD的同名文件合并，分别提供rtc和rtc_lib两个文件
2、增加d2f/iocnt模块

文件内容修改
主要是增加对RN831x_RN861x_MCU_V3、RN821x_RN721x_SOC_V2、RN821x_RN721x_SOC_V3、RN202x_RN7326_SOC_V2系列产品的支持，具体修改点如下：
1、ll驱动对RN831x_RN861x_MCU_V3系列产品默认使用内嵌库函数
2、GPIO修改针对RN202x_RN7326_SOC_V2系列spi应用bug
3、删除多余位GPIO位定义文件gpioremap.h文件
4、iso7816.增加对多个模块的支持
5、sysc.h文件删除多余位操作用宏定义，当前由函数操作，宏定义操作可删除
6、spi针对不同系列编号和名称有差异
7、uart增加对产品系列支持，RN831x_RN861x_MCU_V3支持8路uart,其他仅6路uart
8、emu增加对RN202x_RN7326_SOC_V2系列的支持
9、intc增加对RN202x_RN7326_SOC_V2系列的支持，该系列产品intc只支持中断合并模式
10、madc增加对RN202x_RN7326_SOC_V2系列的支持，该系列产品ADC有7个通道；增加对11、AD_STAT清零成功判断，防止无效清零动作
12、rtc不对头文件定义的寄存器，不再用宏定义操作；避免使用不开放的寄存器；用户模式0下使用13、函数LL_RTC_Mode0TempUpdate重新修订，防止不查询busy就启动sar测温
14、simptc增加对产品系列支持，RN831x_RN861x_MCU_V3同时支持16位和32位的定时器RN831x_RN861x_MCU_V2仅支持16位定时器，其他型号支持32位定时器

------------------------------------------------------------------------------------------------------------------------------------------
V1.3.3  2024-08-21
库函数升级到V1.2.2


更新记录
------------------------------------------------------------------------------------------------------------------------------------------
V1.3.2  2024-08-02

ll驱动库内的库函数升级到V1.2.1
  - 修正部分lint报警
   - 增加`LL_CLKTRIM_RclTrimByRch`,`LL_CLKTRIM_SysClkMonitorAsync`,`LL_CLKTRIM_SysClkMonitorAsyncStatus`,`LL_CLKTRIM_SysClkMonitorSync`,`LL_CLKTRIM_LoscStopCheck`接口
   - 修改bug
    
ll驱动库内的加密库函数升级到V1.0.6
     增加加密库版本返回函数接口

ll驱动库内的源文件
    增加RN202X_RN7326_SOC V2版本芯片支持

------------------------------------------------------------------------------------------------------------------------------------------
V1.3.1  2024-05-31

ll驱动库内的库函数升级到V1.2.0

------------------------------------------------------------------------------------------------------------------------------------------
V1.3.0  2024-01-22
修改GET_DATA_MASK函数，避免移位使用时输入32的报警，修改后移位操作范围是0-31

m2m模块
1、功能修改：修订数据按地址反序排列功能使能位与实际相反；

DSP模块
1、功能优化：命名规范为DSP开头
SPI模块
1、功能增加：SPI模块中增加V2版本芯片位定义支持

EMU模块
1、功能优化：EMU模块删除volatile修饰


------------------------------------------------------------------------------------------------------------------------------------------
V1.2.0  2024-01-03

主要修改
1、功能增加：库函数版本升级到V1.1.6
2、功能优化：CMSIS文件版本升级到V1.0.1
3、功能优化：共用体中结构体命名所有模块统一风格；
4、功能优化：系统控制寄存器模块使能统一外部开启或关闭；
5、功能优化：由于系统控制时钟在外部统一处理，故删除所有模块的deinit函数
6、功能优化：型号定义挪到rn8xxx_ll_config.h内，并根据型号定义模块
7、功能优化：.c和.h文件保持一一对应

emu模块
1、功能优化：EMU函数读写寄存器增加 volatile 修饰；

simptc模块：
1、功能优化：共用体中结构体命名所有模块统一风格；

tc模块：
1、功能优化：共用体中结构体命名所有模块统一风格；

madc模块：
1、功能优化：共用体中结构体命名所有模块统一风格；

kbi模块：
1、功能优化：系统控制寄存器模块使能统一外部开启或关闭；

intc模块：
1、功能优化：系统控制寄存器模块使能统一外部开启或关闭；
2、功能优化：宏定义区分LL_INTC_Init（）函数与LL_Intc_IRQ（）函数，用于区分V1与V2版本区别；

spi模块：
1、功能优化：共用体中结构体命名所有模块统一风格；
2、功能优化：系统控制寄存器模块使能统一外部开启或关闭；
3、功能优化：增加宏定义区分不同版本支持不同的SPI；
4、功能增加：增加Disable函数；

rtc模块：
无

dsp模块：
1、功能优化：共用体中结构体命名所有模块统一风格；

iso7816模块：
1、功能优化：系统控制寄存器模块使能统一外部开启或关闭；

iic模块：
1、初始化函数修改参数：分频系数内部计算，使用者用iic频率；

m2m模块
1、功能修改：修改 LL_M2M_Move入参方式。支持DUMMY值设置；
2、功能优化：共用体中结构体命名所有模块统一风格；
3、功能优化：入参进行合法检测，MCU的目标地址和源地址必须word对齐；

lcd模块：
1、功能优化：共用体中结构体命名所有模块统一风格；

gpio模块：
1、功能优化：GPIO复用功能ID把KEY0到KEY7合并为KEY。INT0到INT7合并位INT；

sysc模块：
1、功能优化：统一不同型号产品第一个SPI为SPI0；

uart模块
1、功能优化：共用体中结构体命名所有模块统一风格；
2、功能增加：增加Disable函数；

wdt模块
无

------------------------------------------------------------------------------------------------------------------------------------------
V1.1.9  2023-11-29
主要修改
1、功能增加：增加编程选项内容操作的函数接口
2、功能优化：架构调整，重新整理库函数，包括按模块区分文件，以及.c和.h成对出现
3、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()
3、功能优化：优化解决GCC提示的共用体中结构体没有名称的warning
4、BUG修订：不同型号编译的报错
5、测试中发现的不妥以及bug；具体如下：

emu模块
1、功能修改：修改 uEmuCommBuf 中变量定义
2、功能优化：LL_EMU_RegRead 函数 形参名称 改为rReg。
3、功能优化：LL_EMU_RegRead、LL_EMU_RegWrite 函数增加芯片类型定义错误时返回 ERN_ERROR。
4、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

simptc模块：
1、BUG修订：更新bit定义改动引起的错误
2、功能修订：将多个simptc模块操作函数合成一个
3、功能增加：增加Disable函数
4、功能优化：增加写入溢出限制
5、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

tc模块：
1、BUG修订：更新bit定义改动引起的错误
2、功能增加：开放pwm功能配置位
3、功能增加：增加Disable函数
4、功能增加：增加中断使能配置
5、功能优化：修改脉宽测量初始化分频设置
6、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

madc模块：
1、BUG修订：更新bit定义改动引起的错误
2、功能增加：增加Disable函数
3、功能优化：删除模块时钟使能
4、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

kbi模块：
1、BUG修订：修正kbi模块一直打开KBI_IRQn中断问题
2、功能增加：模块增加DeInit函数，用于恢复模块寄存器默认值；增加Enable函数，主要用于开启模块时钟
3、功能优化：模块统一使用Disable函数关闭模块
4、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

intc模块：
1、BUG修订：修正增加intc模块未考虑V2版本芯片支持共用外部中断EXT0处理部分
2、功能增加：模块增加DeInit函数，用于恢复模块寄存器默认值；增加Enable函数，主要用于开启模块时钟
3、功能优化：修正部分注释不正确的问题
4、功能优化：模块统一使用Disable函数关闭模块
5、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

spi模块：
1、功能增加：增加了获取spi的id号的函数；
2、功能优化：修改了spi_ctrl寄存器在共用体中的名字；
3、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

rtc模块：
1、功能优化：增加了注释；
2、功能优化：规范化了宏定义；
3、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

dsp模块：
1、BUG修订：修改DSP拉格朗日插值算法不正确bug；
2、功能修改：系统时钟统一在外部开启，减少耦合；
3、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

iso7816模块：
1、BUG修订：修改7816时钟配置不正确bug；
2、功能修改：系统时钟统一在外部开启，减少耦合；
3、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

iic模块：
1、功能增加：增加LL_IIC_ClkDiv_Get函数，通过传入IIC的速率枚举（100K/400K可选）获取Div配置参数。
2、功能修改：IIC函数结构均改为传入I2C_TypeDef *I2Cx，以支持可能后续多个IIC的情况。
3、功能优化：显示的配置参数联合体内的位段增加名字BitLCDCtl。
4、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

m2m模块
无

lcd模块：
1、功能增加：I2c接口增加指针方便扩展I2C个数。
2、功能优化：lcd的位段增加名字。
3、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()。

gpio模块：
1、BUG修订：更改型号选择宏定义错误
2、BUG修订：修改P106错误
3、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()

sysc模块：
1、BUG修订：修订APB时钟开关函数bug
2、功能增加：增加函数接口支持系统控制模块中寄存器SYS_RST、MOD0_EN、MOD1_EN、INTC_EN、KBI_EN的操作
3、功能增加：增加对MCU V2及SOC V2产品的支持，增加相应宏定义
4、功能优化：删除LL_SYSC_SysModeChg函数接口，消除与sysclk中系统时钟切换函数接口功能相同函数不同的困扰
5、功能优化：SYSC模块内函数名Enable修改为Ctrl，语义明确
6、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()。

uart模块
1、功能增加：增加Deinit函数
2、功能优化：共用体中结构体增加名字，消除gcc的warning
3、功能优化：删除多余不用的函数
4、功能增加：增加根据串口号，寻找串口地址的的函数
5、BUG修订：修改uart_ctrl寄存器前关闭uart，防止对控制寄存器操作失败
6、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()。

wdt模块
1、BUG修订：WDT每个寄存器写之前要判断忙标志
2、功能优化：优化满足一些MISRA-C的规则，如注释统一使用/**/，数字后统一加U，宏定义变量外增加()。


------------------------------------------------------------------------------------------------------------------------------------------
V1.1.8  2023-11-06

主要修改点
1、MADC头文件增加LL_ANA_SarADCInit的申明；MADC源和头文件修改LL_ANA_SarADCGet函数接口，删除参数；
2、sea_def.h中修订包含文件名为rn8xxx_ll_def.h
3、删除重复的加密文件


------------------------------------------------------------------------------------------------------------------------------------------
V1.1.7  2023-11-04

主要修改点
1、加密库文件合并
2、CMSIS文件修订，统一命名规范；
3、CMSIS文件修订，修订MISRA报警
4、CMSIS文件修订，修改包含路径表示方式为相对路径
5、修订定时器参数传递不合理
6、修订注释编码问题
7、sar模块中，GET函数拆分成  init和 get两个函数
8、simp_tc模块参数类型修改为寄存器头文件中定义的结构体

------------------------------------------------------------------------------------------------------------------------------------------
V1.1.6  2023-10-27

主要修改点
1、库函数lib和加密库的文件名和函数名统一修改
	所有的文件名都加rn8xxx_ll;其中rtc除加头外，rtc.c修改成rtc_drv,当前函数名为rn8xxx_ll_rtc_drv.c;nvm.c修改成memory,当前文件名为    rn8xxx_ll_memory.c
	所有"开放给客户调用的函数"加LL_模块名(大写)	
2、gpio优化GPIOFunTable结构,节省空间
3、GCC文件warning修改
4、GCC中NULL的warning修改

------------------------------------------------------------------------------------------------------------------------------------------
V1.1.5  2023-10-24

主要修改点
1、产品型号规范
	内部分成RN821x_RN721x_SOC_B RN821x_RN721x_SOC_C RN821x_RN721x_SOC_D RN831x_RN861x_MCU_V2
	外部型号为实际客户下单型号包括
	RN8213_B、RN8215_B、RN8213B_B、RN7213_B
	RN8213_C、RN8211B_C、RN8211_C
	RN8213_D、RN8217_D、RN8213B_D、RN7213_D
	RN8211B_V2、RN8213_V2、RN8215_V2、RN8217_V2、RN8213B_V2、RN7213_V2、RN7213_V2
	RN8318_V2、RN8615_V2、RN8613_V2、RN8611_V2	
      	客户使用该前，
	1）需在rn8xxx_ll_def.h中定义芯片的外部型号，如RN8217_D
	2）需在rn8xxx_ll_confg.h中定义需要使用的模块（已默认开启所有产品都有的模块，指定产品具有的特殊模块（可从用户手册中了解到是否支持），需自定义开	启）		
2、MCU CMSIS文件名修改
3、修订包含确保不会垮型号调用
4、修改GPIO中串口宏定义，统一用UART不用RX和TX
5、删除SPI中重复宏定义
6、GPIO用在SOC_V1版本上时的编译错误修订
7、系统控制中APB时钟开启函数增加函数说明，参数用枚举类型实现
8、rn8xxx_ana模块名修改为rn8xxx_madc与头文件中寄存器定义保持一致

------------------------------------------------------------------------------------------------------------------------------------------
V1.1.4  2023-10-20

主要修改点
1、规范型号区分
2、增加SOC的GCC支持文件及头文件
3、修订包含确保不会垮型号调用
4、删除重复的宏定义
5、ll层含中文注释的文件编码格式改成UTF-8
6、GPIO用在SOC_V1版本上时的编译错误修订
7、SPI文件中函数定义和申明的统一

------------------------------------------------------------------------------------------------------------------------------------------
------------------------------------------------------------------------------------------------------------------------------------------
V1.1.3  2023-10-19

主要修改点
------------------------------------------------------------------------------------------------------------------------------------------
按照以下命名规范修改文件名、函数名、宏定义名
	文件名=型号名（rn8xxx）+层名（ll）+模块名(文件名全部小写)
	函数名=层名（如LL，层名大写）+模块名（模块名大写）+函数功能名（大驼峰法命名）
	宏定义=型号名+层名+模块名（全部大写，型号、层、模块之间用下划线分割，x代表数字所以小写）
------------------------------------------------------------------------------------------------------------------------------------------
V1.1.3  2023-10-19

主要修改点
------------------------------------------------------------------------------------------------------------------------------------------
按照以下命名规范修改文件名、函数名、宏定义名
	文件名=型号名（rn8xxx）+层名（ll）+模块名(文件名全部小写)
	函数名=层名（如LL，层名大写）+模块名（模块名大写）+函数功能名（大驼峰法命名）
	宏定义=型号名+层名+模块名（全部大写，型号、层、模块之间用下划线分割，x代表数字所以小写）


除主要修改点外的其他修改内容
------------------------------------------------------------------------------------------------------------------------------------------
rn8xxx_ll_tc.c
1、增加us的定时函数。
2、修改方式：init函数做固定参数的初始化；set_config函数做可变参数的修改；start/stop函数，做启动/停止操作。
3、fnTimeMeasure()函数，把方波频率参数放到形参。另外，把TimerID的类型修改为TC_TypeDef。

rn8xxx_ll_uart.c
1、删除功能相同的函数（只保留传UART地址的函数）

rn8xxx_ll_spi.c
1、删除功能相同的函数（只保留传SPI地址的函数）

rn8xxx_ll_sysc.c
1、增加快关APB时钟的函数接口

rn8xxx_ll_gpio.c
1、GPIO，宏定义PIN脚ID，改为不用下滑线开始
2、去掉功能选项_LCD,改为只有_SEG， _COM
3、_PIN_Seg0Com4   改为了P124,依次类推

rn8xxx_ll_emu.c
1、EMU模块，寄存器的宏定义，不加双下划线，加LL_EMU_

------------------------------------------------------------------------------------------------------------------------------------------
V1.1.2  2023-10-09

主要修改点
------------------------------------------------------------------------------------------------------------------------------------------
增加模块：Rn8xxx_SimpTC
增加模块：Rn8xxx_EMU
增加文件：Rn8xxx_Hal_Config.h、Rn8xxx_Hal.h（用于HAL驱动管理）
修订Rn8xxx_Def.h，增加对不同芯片头文件的管理
头文件包含根据Rn8xxx_Def.h的配置加载

具体修改内容
------------------------------------------------------------------------------------------------------------------------------------------
Rn8xxx_GPIO
1、更改功能配置的实现方式
2、增加函数接口	
void Rn8xxx_GPIO_Init(GPIO_InitTypeDef *GPIO_Init)
void Rn8xxx_GPIO_CfgInit(uint32_t Pin,eGPIOFunction_TypeDef Mode,
			eGPIOOutputLevel_TypeDef OutputLevel,
			eGPIOPull_TypeDef Pull,
			eGPIODir_TypeDef Dir,
			eGPIOInputMode_TypeDef InputMode,
			eGPIOOutputMode_TypeDef OutputMode)	
void Rn8xxx_GPIO_CfgInputMode(uint8_t pin_id, eGPIOInputMode_TypeDef mode )
void Rn8xxx_GPIO_CfgOutputMode(uint8_t pin_id , eGPIOOutputMode_TypeDef mode )
void Rn8xxx_GPIO_CfgDirMode(uint8_t pin_id ,eGPIODir_TypeDef dir)

Rn8xxx_TC
1、增加了PWM输出函数；
2、增加了脉宽测量函数；

Rn8xxx_Uart
1、根据芯片型号管理不同函数的编译
2、uartid统一成uart_id
3、头文件对齐，运算符号前后增加空格
4、增加函数接口
ErrorStatus LL_UART_Init(UART_TypeDef* UARTx, sLL_UART_InitTypeDef* sUART_Init);
ErrorStatus LL_UARTDMA_Init(UART_TypeDef* UARTx, sLL_UARTDMA_InitTypeDef sUART_DMAInit);
uint16_t LL_UART_DMA_RxInfo(UART_TypeDef* UARTx);
uint16_t LL_UART_DMA_TxInfo(UART_TypeDef* UARTx);

Rn8xxx_Spi
1、根据芯片型号管理不同函数的编译
2、头文件对齐，运算符号前后增加空格
3、增加函数接口
ErrorStatus LL_SPI_Init(SPI_TypeDef* SPIx, sLL_SPI_InitTypeDef* sSPI_Init);
ErrorStatus LL_SPIDMA_Init(SPI_TypeDef* SPIi, sLL_SPIDMA_InitTypeDef* sSPIDMA_Init);
void LL_SPIDMA_TxStar(SPI_TypeDef* SPIx, uint32_t Addr, uint32_t TxLen);
uint32_t LL_SPIDMA_RxInfo(SPI_TypeDef* SPIx);
uint32_t LL_SPIDMA_TxInfo(SPI_TypeDef* SPIx);
uint8_t LL_SPI_ReadByte(SPI_TypeDef* SPIx);
void LL_SPI_WriteByte(SPI_TypeDef* SPIx, uint8_t Dat);

Rn8xxx_WDT
1、增加函数接口
void Rn8xxx_WDT_CfgDef(sWDT_Cfg_TypeDef * Cfg);
void Rn8xxx_WDT_Cfg(sWDT_Cfg_TypeDef *Cfg);
void Rn8xxx_WDT_RloadDefCfg(void);

------------------------------------------------------------------------------------------------------------------------------------------
V1.1.1  2023-09-14
主要修改点
------------------------------------------------------------------------------------------------------------------------------------------
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化
5、增加驱动模块Rn8xxx_Sysc

具体修改内容
------------------------------------------------------------------------------------------------------------------------------------------
Rn8xxx_Common
1、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_DSP
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_GPIO
1、统一变量类型为stdint中定义的名称

Rn8xxx_IIC
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_INTC
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_ISO7816
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_KBI
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_Lcd
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_LvdCmpSar
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_RTC
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_Spi
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_SysTickCortexM0
1、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
2、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_TC
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_Uart
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、统一变量类型为stdint中定义的名称
3、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
4、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化

Rn8xxx_WDT
1、各模块头文件中增加寄存器及寄存器位操作的宏定义
2、宏定义遵循规则 1）全部用大写；2）.c文件中不能做宏定义 3）ERROR等自定义宏，名字前加类型和RN做标识（如枚举类型变量为ERN_ERROR），防止同名
3、包含文件遵循：1）.h文件要能独立编译成功；2）.C中最先包含同名字的.h文件；3）模块化
------------------------------------------------------------------------------------------------------------------------
V1.2.0  2024-01-03
主要修改


emu模块


simptc模块：


tc模块：


madc模块：


kbi模块：

intc模块：

spi模块：


rtc模块：


dsp模块：


iso7816模块：


iic模块：


m2m模块
无

lcd模块：


gpio模块：


sysc模块：


uart模块


wdt模块























