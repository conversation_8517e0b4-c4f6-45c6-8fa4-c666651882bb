/**
 *******************************************************************************
 * @file  template/source/main.h
 * @brief This file contains the including files of main routine.
 @verbatim
   Change Logs:
   Date             Author              Notes
   2023-08-31       Renergy             First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023-2033, Renergy Micro-Technologies(Shenzhen)CO., LTD.
 *  All rights reserved.
 *
 *
 *******************************************************************************
 */
#ifndef __MAIN_H__
#define __MAIN_H__

#include "rn8xxx_ll.h"

//#define LL_LOW_POWER_DEMO
#define LL_UART_DEMO
// #define LL_GPIO_DEMO
// #define LL_INTC_DEMO
// #define LL_KBI_DEMO
// #ifdef LL_M2M_MODULE_ENABLED
// #define LL_M2M_DEMO
// #endif
// #define LL_MADC_DEMO
// #define LL_SMTC_DEMO
// #define LL_SPI_DEMO
// #define LL_SYSC_DEMO
// #define LL_TC_DEMO
// #define LL_WDT_DEMO
// #ifdef LL_EMU_MODULE_ENABLED
// #define LL_EMU_DEMO
// #endif
// #define LL_RTC_DEMO

#if defined(RN831x_RN861x_MCU_V3)
extern const rn_lib_t *rn_lib;
#endif

#if defined(LL_LOW_POWER_DEMO)
#include "rn8xxx_ll_lowpower_demo.h"
#endif

#if defined(LL_GPIO_DEMO)
#include "rn8xxx_ll_GPIO_demo.h"
#endif

#if defined(LL_INTC_DEMO)
#include "rn8xxx_ll_INTC_demo.h"
#endif

#if defined(LL_KBI_DEMO)
#include "rn8xxx_ll_KBI_demo.h"
#endif

#if defined(LL_M2M_DEMO)
#include "rn8xxx_ll_M2M_demo.h"
#endif

#if defined(LL_MADC_DEMO)
#include "rn8xxx_ll_madc_demo.h"
#endif

#if defined(LL_SMTC_DEMO)
#include "rn8xxx_ll_simptc_demo.h"
#endif

#if defined(LL_SPI_DEMO)
#include "rn8xxx_ll_spi_demo.h"
#endif

#if defined(LL_SYSC_DEMO)
#include "rn8xxx_ll_SYSC_demo.h"
#endif

#if defined(LL_TC_DEMO)
#include "rn8xxx_ll_tc_demo.h"
#endif

#if defined(LL_UART_DEMO)
#include "rn8xxx_ll_uart_demo.h"
#endif

#if defined(LL_WDT_DEMO)
#include "rn8xxx_ll_wdt_demo.h"
#endif

#if defined(LL_RTC_DEMO)
#include "rn8xxx_ll_rtc_demo.h"
#endif

#if defined(LL_EMU_DEMO)
#include "rn8xxx_ll_emu_demo.h"
#endif

#endif /* __MAIN_H__ */

/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
